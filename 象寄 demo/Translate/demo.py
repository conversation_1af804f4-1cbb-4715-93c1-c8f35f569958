import cv2
import numpy as np
from paddleocr import PaddleOCR
from PIL import Image, ImageDraw, ImageFont
import os
import re
from core.font_matcher import SimpleFontMatcher
from core.translator import SimpleTranslator
from core.layout_analyzer import LayoutAnalyzer

def is_chinese_text(text):
    """判断文本是否包含中文字符"""
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
    return bool(chinese_pattern.search(text))

def draw_ocr_result_simple(image_path, dt_polys, rec_texts, rec_scores, output_path):
    """在原图上绘制检测框和序号"""
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return
    
    for i, (poly, text, score) in enumerate(zip(dt_polys, rec_texts, rec_scores)):
        points = np.array(poly, dtype=np.int32)
        
        # 判断是否为中文文字
        is_chinese = is_chinese_text(text)
        box_color = (0, 0, 255) if is_chinese else (0, 255, 0)  # 中文用红色，其他用绿色
        
        # 绘制检测框
        cv2.polylines(image, [points], True, box_color, 2)
        
        # 只添加序号
        x, y = points[0]
        label = f"{i+1}"
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
        cv2.rectangle(image, (x, y-25), (x + label_size[0] + 8, y), box_color, -1)
        cv2.putText(image, label, (x + 4, y - 6), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    cv2.imwrite(output_path, image)

def draw_alignment_debug(image_path, dt_polys, rec_texts, rec_scores, font_info, translator, output_path, layout_result=None):
    """绘制原文中心和PIL绘制位置的对比调试图"""
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return
    
    debug_image = image.copy()
    
    for i, (poly, text, score) in enumerate(zip(dt_polys, rec_texts, rec_scores)):
        if not is_chinese_text(text):
            continue
            
        points = np.array(poly, dtype=np.int32)
        x, y, w, h = cv2.boundingRect(points)
        
        # 绘制原始检测框（绿色）
        cv2.rectangle(debug_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
        
        # 计算并绘制原文中心点（红色圆圈）
        orig_center_x = x + w // 2
        orig_center_y = y + h // 2
        cv2.circle(debug_image, (orig_center_x, orig_center_y), 5, (0, 0, 255), -1)
        
        # 查找对应的翻译文字
        translated_text = translator.translate_text(text)
        if translated_text == text:
            continue
            
        # 查找字体信息
        region_font_info = None
        for info in font_info:
            if info['text'] == text:
                region_font_info = info
                break
        
        if not region_font_info:
            continue
            
        # 计算PIL绘制位置（模拟translator的计算逻辑）
        font_path = translator.font_mapping.get(region_font_info['font'])
        if font_path and os.path.exists(font_path):
            try:
                # 提取样式信息
                style_info = translator.extract_text_style(image, poly)
                base_font_size = max(style_info['estimated_font_size'], 16)
                
                # 计算ROI真实高度（与实际翻译过程保持一致）
                roi = image[y:y+h, x:x+w].copy()
                real_h = translator.measure_roi_text_height(roi, prefix=f"debug_roi_{i}")
                if real_h > 0:
                    target_height = real_h  # 使用真实笔画高度
                else:
                    target_height = h  # 退化到OCR外接矩形高度
                
                # 计算字体大小
                font_size = translator.calculate_font_size_by_height_matching(
                    translated_text, target_height, font_path, base_font_size
                )
                
                # 获取文字尺寸
                text_width, text_height = translator.get_text_dimensions(
                    translated_text, font_path, font_size
                )
                
                # 计算PIL绘制位置（居中）
                pil_text_x = x + (w - text_width) // 2
                pil_text_y = y + (h - text_height) // 2
                pil_text_x = max(0, pil_text_x)
                pil_text_y = max(0, pil_text_y)
                
                # 绘制PIL文字位置框（蓝色）
                cv2.rectangle(debug_image, (pil_text_x, pil_text_y), 
                             (pil_text_x + text_width, pil_text_y + text_height), (255, 0, 0), 2)
                
                # 计算并绘制PIL文字中心点（蓝色圆圈）
                pil_center_x = pil_text_x + text_width // 2
                pil_center_y = pil_text_y + text_height // 2
                cv2.circle(debug_image, (pil_center_x, pil_center_y), 5, (255, 0, 0), -1)
                
                # 绘制布局感知的译文文本框区域（紫色）
                if layout_result:
                    try:
                        # 使用与实际翻译相同的布局感知逻辑
                        mock_region = {
                            'bbox': (x, y, w, h),
                            'original_text': text,
                            'translated_text': translated_text
                        }
                        
                        # 确定对齐方式
                        center_x = x + w // 2
                        h_align = layout_result.get('horizontal_alignment', {})
                        alignment_type = 'center'  # 默认居中
                        
                        # 检查左对齐组
                        left_groups = h_align.get('left_groups', [])
                        for group in left_groups:
                            for group_region in group:
                                if (abs(group_region['left'] - x) <= 5 and 
                                    abs(group_region['top'] - y) <= 5):
                                    alignment_type = 'left'
                                    break
                        
                        # 检查居中对齐组  
                        if alignment_type != 'left':
                            center_groups = h_align.get('center_groups', [])
                            for group in center_groups:
                                for group_region in group:
                                    group_center_x = group_region['center'][0]
                                    if abs(group_center_x - center_x) <= 5:
                                        alignment_type = 'center'
                                        break
                        
                        # 检查右对齐组
                        if alignment_type not in ['left', 'center']:
                            right_groups = h_align.get('right_groups', [])
                            for group in right_groups:
                                for group_region in group:
                                    if (abs(group_region['right'] - (x + w)) <= 5 and
                                        abs(group_region['top'] - y) <= 5):
                                        alignment_type = 'right'
                                        break
                        
                        # 根据对齐方式计算译文位置
                        if alignment_type == 'left':
                            final_text_x = x  # 左对齐
                        elif alignment_type == 'right':
                            final_text_x = x + w - text_width  # 右对齐
                        else:  # 'center'
                            final_text_x = x + (w - text_width) // 2  # 居中对齐
                        
                        final_text_y = y + (h - text_height) // 2  # 垂直居中
                        
                        # 确保位置合理
                        final_text_x = max(0, final_text_x)
                        final_text_y = max(0, final_text_y)
                        
                        # 绘制译文文本框（紫色）
                        cv2.rectangle(debug_image, (final_text_x, final_text_y), 
                                     (final_text_x + text_width, final_text_y + text_height), (255, 0, 255), 2)
                        
                        # 绘制译文中心点（紫色圆圈）
                        final_center_x = final_text_x + text_width // 2
                        final_center_y = final_text_y + text_height // 2
                        cv2.circle(debug_image, (final_center_x, final_center_y), 5, (255, 0, 255), -1)
                        
                        # 绘制从原文中心到译文中心的连线（青色）
                        cv2.line(debug_image, (orig_center_x, orig_center_y), (final_center_x, final_center_y), (255, 255, 0), 2)
                        
                        print(f"  布局感知译文位置: ({final_text_x}, {final_text_y}), 对齐方式: {alignment_type}")
                        
                    except Exception as e:
                        print(f"绘制布局感知译文文本框失败: {e}")
                        # 如果出错，绘制原有的连线
                        cv2.line(debug_image, (orig_center_x, orig_center_y), (pil_center_x, pil_center_y), (255, 255, 0), 2)
                else:
                    # 如果没有布局分析结果，绘制原有的连线
                    cv2.line(debug_image, (orig_center_x, orig_center_y), (pil_center_x, pil_center_y), (255, 255, 0), 2)
                
                # 计算偏移距离
                offset_x = pil_center_x - orig_center_x
                offset_y = pil_center_y - orig_center_y
                offset_distance = np.sqrt(offset_x**2 + offset_y**2)
                
                print(f"对齐调试 - '{text}' → '{translated_text}':")
                print(f"  原文中心: ({orig_center_x}, {orig_center_y})")
                print(f"  PIL中心: ({pil_center_x}, {pil_center_y})")
                print(f"  偏移: ({offset_x:+d}, {offset_y:+d}) = {offset_distance:.1f}px")
                print(f"  字体大小: {font_size}px, 文字尺寸: {text_width}x{text_height}")
                
            except Exception as e:
                print(f"计算PIL位置失败: {e}")
    
    # 转换为PIL图像进行中文标签绘制
    pil_image = Image.fromarray(cv2.cvtColor(debug_image, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(pil_image)
    
    # 准备绘制标签的字体
    label_font = None
    try:
        import platform
        sys_name = platform.system()
        if sys_name == "Windows":
            label_font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 14)
        elif sys_name == "Darwin":
            label_font = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 14)
        else:
            # 常见 Noto Sans CJK 路径
            label_font = ImageFont.truetype("/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc", 14)
    except Exception:
        pass

    # 如果系统字体加载失败，尝试使用项目内字体
    if label_font is None:
        project_fonts = {
            "思源黑体": "fonts/思源黑体/SourceHanSans-VF.otf",
            "台北黑体": "fonts/台北黑体/TaipeiSans-Bold.ttf",
            "NotoSans": "fonts/NotoSansSC/NotoSansSC-Black.ttf"
        }
        for font_name, font_path in project_fonts.items():
            if os.path.exists(font_path):
                try:
                    label_font = ImageFont.truetype(font_path, 14)
                    break
                except Exception:
                    continue

    # 最后退回默认字体
    if label_font is None:
        try:
            label_font = ImageFont.load_default()
        except Exception:
            label_font = None
    
    # 重新绘制中文标签和文字信息
    for i, (poly, text, score) in enumerate(zip(dt_polys, rec_texts, rec_scores)):
        if not is_chinese_text(text):
            continue
            
        points = np.array(poly, dtype=np.int32)
        x, y, w, h = cv2.boundingRect(points)
        
        # 计算原文中心点
        orig_center_x = x + w // 2
        orig_center_y = y + h // 2
        
        # 查找对应的翻译文字
        translated_text = translator.translate_text(text)
        if translated_text == text:
            continue
            
        # 查找字体信息
        region_font_info = None
        for info in font_info:
            if info['text'] == text:
                region_font_info = info
                break
        
        if not region_font_info:
            continue
            
        # 计算PIL绘制位置
        font_path = translator.font_mapping.get(region_font_info['font'])
        if font_path and os.path.exists(font_path):
            try:
                # 提取样式信息
                style_info = translator.extract_text_style(image, poly)
                base_font_size = max(style_info['estimated_font_size'], 16)
                
                # 计算字体大小
                font_size = translator.calculate_font_size_by_height_matching(
                    translated_text, h, font_path, base_font_size
                )
                
                # 获取文字尺寸
                text_width, text_height = translator.get_text_dimensions(
                    translated_text, font_path, font_size
                )
                
                # 计算PIL绘制位置（居中）
                pil_text_x = x + (w - text_width) // 2
                pil_text_y = y + (h - text_height) // 2
                pil_text_x = max(0, pil_text_x)
                pil_text_y = max(0, pil_text_y)
                
                # 计算PIL文字中心点
                pil_center_x = pil_text_x + text_width // 2
                pil_center_y = pil_text_y + text_height // 2
                
                # 计算偏移距离
                offset_x = pil_center_x - orig_center_x
                offset_y = pil_center_y - orig_center_y
                offset_distance = np.sqrt(offset_x**2 + offset_y**2)
                
                # 使用Pillow绘制中文标签
                if label_font:
                    # 绘制"原文中心"标签
                    try:
                        draw.text((orig_center_x + 10, orig_center_y - 10), "原文中心", 
                                fill=(0, 0, 255), font=label_font)
                    except:
                        draw.text((orig_center_x + 10, orig_center_y - 10), "原文中心", 
                                fill=(0, 0, 255))
                    
                    # 绘制"PIL中心"标签
                    try:
                        draw.text((pil_center_x + 10, pil_center_y + 10), "PIL中心", 
                                fill=(255, 0, 0), font=label_font)
                    except:
                        draw.text((pil_center_x + 10, pil_center_y + 10), "PIL中心", 
                                fill=(255, 0, 0))
                    
                    # 绘制"译文中心"标签（如果有布局分析结果）
                    if layout_result:
                        try:
                            # 使用与绘制部分相同的布局感知逻辑
                            center_x = x + w // 2
                            h_align = layout_result.get('horizontal_alignment', {})
                            alignment_type = 'center'  # 默认居中
                            
                            # 检查左对齐组
                            left_groups = h_align.get('left_groups', [])
                            for group in left_groups:
                                for group_region in group:
                                    if (abs(group_region['left'] - x) <= 5 and 
                                        abs(group_region['top'] - y) <= 5):
                                        alignment_type = 'left'
                                        break
                            
                            # 检查居中对齐组  
                            if alignment_type != 'left':
                                center_groups = h_align.get('center_groups', [])
                                for group in center_groups:
                                    for group_region in group:
                                        group_center_x = group_region['center'][0]
                                        if abs(group_center_x - center_x) <= 5:
                                            alignment_type = 'center'
                                            break
                            
                            # 检查右对齐组
                            if alignment_type not in ['left', 'center']:
                                right_groups = h_align.get('right_groups', [])
                                for group in right_groups:
                                    for group_region in group:
                                        if (abs(group_region['right'] - (x + w)) <= 5 and
                                            abs(group_region['top'] - y) <= 5):
                                            alignment_type = 'right'
                                            break
                            
                            # 根据对齐方式计算译文位置
                            if alignment_type == 'left':
                                final_text_x = x  # 左对齐
                            elif alignment_type == 'right':
                                final_text_x = x + w - text_width  # 右对齐
                            else:  # 'center'
                                final_text_x = x + (w - text_width) // 2  # 居中对齐
                            
                            final_text_y = y + (h - text_height) // 2  # 垂直居中
                            final_text_x = max(0, final_text_x)
                            final_text_y = max(0, final_text_y)
                            
                            final_center_x = final_text_x + text_width // 2
                            final_center_y = final_text_y + text_height // 2
                            
                            try:
                                draw.text((final_center_x + 10, final_center_y - 25), "译文中心", 
                                        fill=(255, 0, 255), font=label_font)
                            except:
                                draw.text((final_center_x + 10, final_center_y - 25), "译文中心", 
                                        fill=(255, 0, 255))
                        except:
                            pass
                    
                    # 显示偏移信息
                    info_text = f"偏移: ({offset_x:+d}, {offset_y:+d}) = {offset_distance:.1f}px"
                    try:
                        draw.text((x, y - 60), info_text, fill=(255, 255, 255), font=label_font)
                    except:
                        draw.text((x, y - 60), info_text, fill=(255, 255, 255))
                    
                    try:
                        draw.text((x, y - 45), f"原文: {text}", fill=(255, 255, 255), font=label_font)
                    except:
                        draw.text((x, y - 45), f"原文: {text}", fill=(255, 255, 255))
                    
                    try:
                        draw.text((x, y - 30), f"译文: {translated_text}", fill=(255, 255, 255), font=label_font)
                    except:
                        draw.text((x, y - 30), f"译文: {translated_text}", fill=(255, 255, 255))
                
            except Exception as e:
                print(f"绘制标签失败: {e}")
    
    # 添加图例 - 使用Pillow绘制
    legend_y = 30
    # 创建图例背景（增加高度以容纳新的图例项）
    legend_bg = Image.new('RGBA', (320, 150), (0, 0, 0, 200))
    pil_image.paste(legend_bg, (10, 10), legend_bg)
    
    if label_font:
        try:
            draw.text((20, legend_y), "图例:", fill=(255, 255, 255), font=label_font)
            draw.text((50, legend_y + 18), "原文检测框（绿色）", fill=(0, 255, 0), font=label_font)
            draw.text((50, legend_y + 33), "PIL绘制框（蓝色）", fill=(0, 0, 255), font=label_font)
            draw.text((50, legend_y + 48), "译文文本框（紫色）", fill=(255, 0, 255), font=label_font)
            draw.text((50, legend_y + 63), "原文中心（红色）", fill=(255, 0, 0), font=label_font)
            draw.text((50, legend_y + 78), "PIL中心（蓝色）", fill=(0, 0, 255), font=label_font)
            draw.text((50, legend_y + 93), "译文中心（紫色）", fill=(255, 0, 255), font=label_font)
            draw.text((50, legend_y + 108), "对齐连线（黄色）", fill=(255, 255, 0), font=label_font)
        except:
            # 如果中文绘制失败，使用英文
            draw.text((20, legend_y), "Legend:", fill=(255, 255, 255), font=label_font)
            draw.text((50, legend_y + 18), "Original Box (Green)", fill=(0, 255, 0), font=label_font)
            draw.text((50, legend_y + 33), "PIL Box (Blue)", fill=(0, 0, 255), font=label_font)
            draw.text((50, legend_y + 48), "Translation Box (Purple)", fill=(255, 0, 255), font=label_font)
            draw.text((50, legend_y + 63), "Original Center (Red)", fill=(255, 0, 0), font=label_font)
            draw.text((50, legend_y + 78), "PIL Center (Blue)", fill=(0, 0, 255), font=label_font)
            draw.text((50, legend_y + 93), "Translation Center (Purple)", fill=(255, 0, 255), font=label_font)
            draw.text((50, legend_y + 108), "Alignment Line (Yellow)", fill=(255, 255, 0), font=label_font)
    
    # 转换回OpenCV格式并保存
    final_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    cv2.imwrite(output_path, final_image)
    print(f"对齐调试图已保存: {output_path}")

def draw_layout_analysis_debug(image_path, layout_result, output_path):
    """绘制布局分析调试图"""
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return
    
    debug_image = image.copy()
    
    # 转换为PIL图像进行中文标签绘制
    pil_image = Image.fromarray(cv2.cvtColor(debug_image, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(pil_image)
    
    # 准备绘制标签的字体
    label_font = None
    try:
        import platform
        sys_name = platform.system()
        if sys_name == "Windows":
            label_font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 16)
        elif sys_name == "Darwin":
            label_font = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 16)
        else:
            label_font = ImageFont.truetype("/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc", 16)
    except Exception:
        pass

    if label_font is None:
        try:
            label_font = ImageFont.load_default()
        except Exception:
            label_font = None
    
    # 绘制文字区域和编号
    regions = layout_result['regions']
    for i, region in enumerate(regions):
        x, y, w, h = region['bbox']
        
        # 绘制区域框
        cv2.rectangle(debug_image, (x, y), (x + w, y + h), (0, 255, 255), 2)
        
        # 绘制中心点
        center_x, center_y = region['center']
        cv2.circle(debug_image, (center_x, center_y), 4, (255, 0, 255), -1)
        
        # 使用Pillow绘制文字标签
        if label_font:
            try:
                # 绘制区域编号和文字
                label_text = f"{i+1}: {region['text']}"
                draw.text((x, y - 25), label_text, fill=(255, 255, 0), font=label_font)
            except:
                draw.text((x, y - 25), f"{i+1}", fill=(255, 255, 0))
    
    # 绘制对齐线
    h_align = layout_result['horizontal_alignment']
    
    # 绘制左对齐线（绿色）
    if h_align.get('left_groups'):
        for group in h_align['left_groups']:
            if len(group) >= 2:
                left_x = group[0]['left']
                min_y = min(r['top'] for r in group) - 10
                max_y = max(r['bottom'] for r in group) + 10
                cv2.line(debug_image, (left_x, min_y), (left_x, max_y), (0, 255, 0), 3)
    
    # 绘制居中对齐线（红色）
    if h_align.get('center_groups'):
        for group in h_align['center_groups']:
            if len(group) >= 2:
                center_x = group[0]['center'][0]
                min_y = min(r['top'] for r in group) - 10
                max_y = max(r['bottom'] for r in group) + 10
                cv2.line(debug_image, (center_x, min_y), (center_x, max_y), (255, 0, 0), 3)
    
    # 绘制右对齐线（蓝色）
    if h_align.get('right_groups'):
        for group in h_align['right_groups']:
            if len(group) >= 2:
                right_x = group[0]['right']
                min_y = min(r['top'] for r in group) - 10
                max_y = max(r['bottom'] for r in group) + 10
                cv2.line(debug_image, (right_x, min_y), (right_x, max_y), (255, 255, 0), 3)
    
    # 更新PIL图像
    pil_image = Image.fromarray(cv2.cvtColor(debug_image, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(pil_image)
    
    # 绘制分组框和信息
    # 绘制左对齐组框（红色）
    if h_align.get('left_groups'):
        for group_idx, group in enumerate(h_align['left_groups']):
            if len(group) >= 2:
                # 计算组的边界框
                min_x = min(r['left'] for r in group) - 5
                max_x = max(r['right'] for r in group) + 5
                min_y = min(r['top'] for r in group) - 5
                max_y = max(r['bottom'] for r in group) + 5
                
                # 绘制分组框
                cv2.rectangle(debug_image, (min_x, min_y), (max_x, max_y), (0, 0, 255), 2)
                
                # 绘制分组标签
                if label_font:
                    try:
                        draw.text((min_x, min_y - 25), f"左对齐组{group_idx+1}", fill=(0, 0, 255), font=label_font)
                    except:
                        draw.text((min_x, min_y - 25), f"L{group_idx+1}", fill=(0, 0, 255))
    
    # 绘制居中对齐组框（绿色）
    if h_align.get('center_groups'):
        for group_idx, group in enumerate(h_align['center_groups']):
            if len(group) >= 2:
                # 计算组的边界框
                min_x = min(r['left'] for r in group) - 5
                max_x = max(r['right'] for r in group) + 5
                min_y = min(r['top'] for r in group) - 5
                max_y = max(r['bottom'] for r in group) + 5
                
                # 绘制分组框
                cv2.rectangle(debug_image, (min_x, min_y), (max_x, max_y), (0, 255, 0), 2)
                
                # 绘制分组标签
                if label_font:
                    try:
                        draw.text((min_x, min_y - 25), f"居中对齐组{group_idx+1}", fill=(0, 255, 0), font=label_font)
                    except:
                        draw.text((min_x, min_y - 25), f"C{group_idx+1}", fill=(0, 255, 0))
    
    # 绘制右对齐组框（蓝色）
    if h_align.get('right_groups'):
        for group_idx, group in enumerate(h_align['right_groups']):
            if len(group) >= 2:
                # 计算组的边界框
                min_x = min(r['left'] for r in group) - 5
                max_x = max(r['right'] for r in group) + 5
                min_y = min(r['top'] for r in group) - 5
                max_y = max(r['bottom'] for r in group) + 5
                
                # 绘制分组框
                cv2.rectangle(debug_image, (min_x, min_y), (max_x, max_y), (255, 255, 0), 2)
                
                # 绘制分组标签
                if label_font:
                    try:
                        draw.text((min_x, min_y - 25), f"右对齐组{group_idx+1}", fill=(255, 255, 0), font=label_font)
                    except:
                        draw.text((min_x, min_y - 25), f"R{group_idx+1}", fill=(255, 255, 0))
    
    # 更新PIL图像
    pil_image = Image.fromarray(cv2.cvtColor(debug_image, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(pil_image)
    
    # 添加布局信息面板
    info_panel_height = 160
    info_panel = Image.new('RGBA', (450, info_panel_height), (0, 0, 0, 180))
    pil_image.paste(info_panel, (10, 10), info_panel)
    
    if label_font:
        try:
            y_offset = 20
            draw.text((20, y_offset), f"布局分析结果", fill=(255, 255, 255), font=label_font)
            y_offset += 20
            
            draw.text((20, y_offset), f"布局模式: {layout_result['layout_mode']}", fill=(255, 255, 255), font=label_font)
            y_offset += 20
            
            draw.text((20, y_offset), f"文字区域: {len(regions)}个", fill=(255, 255, 255), font=label_font)
            y_offset += 20
            
            draw.text((20, y_offset), f"水平对齐: {h_align['type']}", fill=(255, 255, 255), font=label_font)
            y_offset += 20
            
            # 显示分组信息
            left_count = len(h_align.get('left_groups', []))
            center_count = len(h_align.get('center_groups', []))
            right_count = len(h_align.get('right_groups', []))
            draw.text((20, y_offset), f"分组: 左{left_count}组 居中{center_count}组 右{right_count}组", fill=(255, 255, 255), font=label_font)
            y_offset += 20
            
            v_dist = layout_result['vertical_distribution']
            draw.text((20, y_offset), f"垂直分布: {v_dist['rows']}行×{v_dist['columns']}列", fill=(255, 255, 255), font=label_font)
        except:
            # 如果中文绘制失败，使用英文
            draw.text((20, 20), f"Layout: {layout_result['layout_mode']}", fill=(255, 255, 255), font=label_font)
            draw.text((20, 40), f"Regions: {len(regions)}", fill=(255, 255, 255), font=label_font)
            draw.text((20, 60), f"Alignment: {h_align['type']}", fill=(255, 255, 255), font=label_font)
    
    # 转换回OpenCV格式并保存
    final_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    cv2.imwrite(output_path, final_image)
    print(f"布局分析调试图已保存: {output_path}")

def draw_grouping_debug(image_path, layout_result, processed_regions, output_path):
    """
    绘制分组信息调试图：显示翻译后的分组框、序号和约束信息
    """
    print(f"生成分组信息调试图: {output_path}")
    
    # 读取原始图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return
    
    # 创建调试图像副本
    debug_image = image.copy()
    
    # 转换为PIL图像进行中文标签绘制
    pil_image = Image.fromarray(cv2.cvtColor(debug_image, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(pil_image)
    
    # 准备绘制标签的字体
    label_font = None
    try:
        import platform
        sys_name = platform.system()
        if sys_name == "Windows":
            label_font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 14)
        elif sys_name == "Darwin":
            label_font = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 14)
        else:
            label_font = ImageFont.truetype("/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc", 14)
    except Exception:
        pass

    # 如果系统字体加载失败，尝试使用项目内字体
    if label_font is None:
        project_fonts = {
            "思源黑体": "fonts/思源黑体/SourceHanSans-VF.otf",
            "台北黑体": "fonts/台北黑体/TaipeiSans-Bold.ttf",
            "NotoSans": "fonts/NotoSansSC/NotoSansSC-Black.ttf"
        }
        for font_name, font_path in project_fonts.items():
            if os.path.exists(font_path):
                try:
                    label_font = ImageFont.truetype(font_path, 14)
                    break
                except Exception:
                    continue

    # 最后退回默认字体
    if label_font is None:
        try:
            label_font = ImageFont.load_default()
        except Exception:
            label_font = None
    
    # 按分组键分组显示
    groups = {}
    for region in processed_regions:
        group_key = region.get('group_key', 'unknown')
        if group_key not in groups:
            groups[group_key] = []
        groups[group_key].append(region)
    
    # 为每个分组分配颜色
    group_colors = [
        (255, 0, 0),    # 红色
        (0, 255, 0),    # 绿色
        (0, 0, 255),    # 蓝色
        (255, 255, 0),  # 黄色
        (255, 0, 255),  # 紫色
        (0, 255, 255),  # 青色
        (255, 128, 0),  # 橙色
        (128, 0, 255),  # 紫红色
    ]
    
    group_idx = 0
    for group_key, group_regions in groups.items():
        if group_key == 'unknown':
            continue
            
        color = group_colors[group_idx % len(group_colors)]
        group_idx += 1
        
        # 计算分组的边界框
        min_x = min(r['bbox'][0] for r in group_regions) - 10
        max_x = max(r['bbox'][0] + r['bbox'][2] for r in group_regions) + 10
        min_y = min(r['bbox'][1] for r in group_regions) - 10
        max_y = max(r['bbox'][1] + r['bbox'][3] for r in group_regions) + 10
        
        # 绘制分组框
        cv2.rectangle(debug_image, (min_x, min_y), (max_x, max_y), color, 3)
        
        # 绘制分组信息
        scale_factor = group_regions[0].get('group_scale_factor', 1.0)
        
        group_info = f"组{group_idx}: {group_key}"
        scale_info = f"缩放: {scale_factor:.2f}"
        
        if label_font:
            try:
                draw.text((min_x, min_y - 40), group_info, fill=color, font=label_font)
                draw.text((min_x, min_y - 25), scale_info, fill=color, font=label_font)
            except:
                draw.text((min_x, min_y - 40), f"Group{group_idx}", fill=color)
                draw.text((min_x, min_y - 25), f"Scale{scale_factor:.2f}", fill=color)
        
        # 绘制组内每个区域的序号和详细信息
        for i, region in enumerate(group_regions):
            x, y, w, h = region['bbox']
            text_x = region.get('text_x', x)
            text_y = region.get('text_y', y)
            
            # 绘制原文边框（细线）
            cv2.rectangle(debug_image, (x, y), (x + w, y + h), color, 1)
            
            # 绘制译文边框（粗线）
            text_width = region.get('text_width', w)
            text_height = region.get('text_height', h)
            cv2.rectangle(debug_image, (text_x, text_y), 
                         (text_x + text_width, text_y + text_height), color, 2)
            
            # 绘制序号
            if label_font:
                try:
                    draw.text((x, y - 15), f"{i+1}", fill=color, font=label_font)
                except:
                    draw.text((x, y - 15), f"{i+1}", fill=color)
            
            # 打印详细信息
            print(f"  组{group_idx}-{i+1}: '{region['original_text']}' → '{region['translated_text']}'")
            print(f"    原文: ({x}, {y}, {w}, {h})")
            print(f"    译文: ({text_x}, {text_y}, {text_width}, {text_height})")
            print(f"    字体: {region.get('font_size', 'unknown')}px")
    
    # 转换回OpenCV格式
    debug_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    
    # 添加信息面板
    pil_image = Image.fromarray(cv2.cvtColor(debug_image, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(pil_image)
    
    info_panel_height = 60 + len(groups) * 20
    info_panel = Image.new('RGBA', (400, info_panel_height), (0, 0, 0, 180))
    pil_image.paste(info_panel, (10, 10), info_panel)
    
    if label_font:
        try:
            y_offset = 20
            draw.text((20, y_offset), f"分组约束结果", fill=(255, 255, 255), font=label_font)
            y_offset += 20
            
            draw.text((20, y_offset), f"总共 {len(groups)} 个分组", fill=(255, 255, 255), font=label_font)
            y_offset += 20
            
            # 显示每个分组的详细信息
            for i, (group_key, group_regions) in enumerate(groups.items()):
                if group_key == 'unknown':
                    continue
                scale_factor = group_regions[0].get('group_scale_factor', 1.0)
                color = group_colors[i % len(group_colors)]
                
                group_text = f"组{i+1}: {len(group_regions)}个文字, {group_key}, 缩放{scale_factor:.2f}"
                draw.text((20, y_offset), group_text, fill=color, font=label_font)
                y_offset += 20
        except:
            # 如果中文绘制失败，使用英文
            draw.text((20, 20), f"Grouping Results", fill=(255, 255, 255), font=label_font)
            draw.text((20, 40), f"Total Groups: {len(groups)}", fill=(255, 255, 255), font=label_font)
    
    # 转换回OpenCV格式并保存
    final_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    cv2.imwrite(output_path, final_image)
    print(f"分组信息调试图已保存: {output_path}")

def draw_alignment_debug_with_groups(image_path, dt_polys, rec_texts, rec_scores, font_info, translator, processed_regions, output_path, layout_result=None):
    """
    绘制简洁的对齐调试图：只显示原文检测框和译文文本框
    """
    print(f"生成对齐调试图: {output_path}")
    
    # 读取原始图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return
    
    # 创建调试图像副本
    debug_image = image.copy()
    
    # 绘制所有检测到的文字边框（绿色）
    for poly, text, score in zip(dt_polys, rec_texts, rec_scores):
        if is_chinese_text(text):
            points = np.array(poly, dtype=np.int32)
            cv2.polylines(debug_image, [points], True, (0, 255, 0), 2)  # 绿色边框
    
    # 绘制译文文本框（紫色）
    for region in processed_regions:
        x, y, w, h = region['bbox']
        text_x = region.get('text_x', x)
        text_y = region.get('text_y', y)
        text_width = region.get('text_width', w)
        text_height = region.get('text_height', h)
        
        # 绘制译文边框
        cv2.rectangle(debug_image, (text_x, text_y), 
                     (text_x + text_width, text_y + text_height), 
                     (255, 0, 255), 2)  # 紫色边框
        
        # 打印简要信息
        print(f"  '{region['original_text']}' → '{region['translated_text']}':")
        print(f"    原文位置: ({x}, {y}, {w}, {h})")
        print(f"    译文位置: ({text_x}, {text_y}, {text_width}, {text_height})")
        print(f"    字体大小: {region.get('font_size', 'unknown')}px")
    
    # 转换为PIL图像进行中文标签绘制
    pil_image = Image.fromarray(cv2.cvtColor(debug_image, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(pil_image)
    
    # 准备绘制标签的字体
    label_font = None
    try:
        import platform
        sys_name = platform.system()
        if sys_name == "Windows":
            label_font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 14)
        elif sys_name == "Darwin":
            label_font = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 14)
        else:
            label_font = ImageFont.truetype("/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc", 14)
    except Exception:
        pass

    # 如果系统字体加载失败，尝试使用项目内字体
    if label_font is None:
        project_fonts = {
            "思源黑体": "fonts/思源黑体/SourceHanSans-VF.otf",
            "台北黑体": "fonts/台北黑体/TaipeiSans-Bold.ttf",
            "NotoSans": "fonts/NotoSansSC/NotoSansSC-Black.ttf"
        }
        for font_name, font_path in project_fonts.items():
            if os.path.exists(font_path):
                try:
                    label_font = ImageFont.truetype(font_path, 14)
                    break
                except Exception:
                    continue

    # 最后退回默认字体
    if label_font is None:
        try:
            label_font = ImageFont.load_default()
        except Exception:
            label_font = None
    
    # 添加简洁的图例
    legend_y = 30
    legend_items = [
        ("原文检测框", (0, 255, 0)),
        ("译文文本框", (255, 0, 255))
    ]
    
    if label_font:
        for i, (label, color) in enumerate(legend_items):
            y_pos = legend_y + i * 25
            try:
                draw.text((10, y_pos), f"● {label}", fill=color, font=label_font)
            except:
                draw.text((10, y_pos), f"● {label}", fill=color)
    
    # 转换回OpenCV格式并保存
    debug_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    cv2.imwrite(output_path, debug_image)
    print(f"对齐调试图已保存: {output_path}")

# 全局OCR实例，避免重复初始化
_ocr_instance = None

def get_ocr_instance():
    """获取OCR实例，避免重复初始化"""
    global _ocr_instance
    if _ocr_instance is None:
        print("初始化PaddleOCR...")
        try:
            _ocr_instance = PaddleOCR(
                use_doc_orientation_classify=False,
                use_doc_unwarping=False,
                use_textline_orientation=False
            )
            print("PaddleOCR初始化完成")
        except Exception as e:
            print(f"PaddleOCR初始化失败: {e}")
            return None
    return _ocr_instance

def process_image_with_latest_paddleocr(font_weight=400):
    """
    基于最新PaddleOCR API的图像处理
    
    参数:
    font_weight: 思源黑体可变字体粗细值 (100-900)
                100=超细, 200=细, 300=较细, 400=正常, 500=中等, 
                600=半粗, 700=粗, 800=超粗, 900=黑体
    """
    
    # 获取OCR实例
    ocr = get_ocr_instance()
    if ocr is None:
        print("OCR初始化失败，程序退出")
        return
    
    # 检查输入文件
    img_path = 'example.jpg'
    if not os.path.exists(img_path):
        print(f"找不到图像文件: {img_path}")
        return
    
    print(f"处理图像: {img_path}")
    
    # OCR处理
    result = ocr.predict(input=img_path)
    if not result:
        print("未检测到文字")
        return
    
    # 创建输出目录
    os.makedirs("output", exist_ok=True)
    
    # 读取图像用于后续处理
    cv_image = cv2.imread(img_path)
    
    # 处理结果
    for res in result:
        
        # 提取OCR数据
        if 'rec_texts' in res and 'dt_polys' in res:
            rec_texts = res['rec_texts']
            dt_polys = res['dt_polys'] 
            rec_scores = res.get('rec_scores', [1.0] * len(rec_texts))
            
            # 初始化字体匹配器和翻译器
            print("初始化字体匹配器...")
            font_matcher = SimpleFontMatcher()
            print(f"初始化翻译器 (字体粗细: {font_weight})...")
            translator = SimpleTranslator(weight_adjustment=font_weight)

            # 如果存在完整可变字体，动态补充映射，确保日文支持
            vf_path = os.path.join('fonts', '思源黑体', 'SourceHanSans-VF.otf')
            if os.path.exists(vf_path):
                # 重新映射所有思源黑体相关字体到可变字体
                source_han_keys = [
                    '思源黑体',
                    '思源黑体 Medium', 
                    'Source Han Sans Bold',
                    'Source Han Sans',
                    'SourceHanSans',
                    'SourceHanSansCN',
                    'Source Han Sans CN',
                    'Noto Sans CJK SC',
                    'Noto Sans SC'
                ]
                for k in source_han_keys:
                    translator.font_mapping[k] = vf_path
                print(f"已加载全语言可变字体映射 (粗细={font_weight}): {source_han_keys}")
            
            # 分类统计和显示检测结果
            chinese_count = 0
            other_count = 0
            chinese_polys = []  # 存储中文文字的多边形坐标
            font_info = []  # 存储字体匹配信息
            
            for j, (text, poly, score) in enumerate(zip(rec_texts, dt_polys, rec_scores), 1):
                is_chinese = is_chinese_text(text)
                lang_type = "中文" if is_chinese else "其他语言"
                print(f"  {j}. '{text}' (置信度: {score:.3f}) [{lang_type}]")
                
                if is_chinese:
                    chinese_count += 1
                    chinese_polys.append(poly)
                    
                    # 提取文字区域进行字体匹配
                    points = np.array(poly, dtype=np.int32)
                    x, y, w, h = cv2.boundingRect(points)
                    text_region = cv_image[y:y+h, x:x+w]
                    
                    # 字体匹配
                    if text_region.size > 0:  # 确保区域有效
                        matched_font, font_confidence = font_matcher.match_font(text_region, text)
                        
                        # 获取匹配字体的真实文件路径
                        font_path = None
                        if matched_font in font_matcher.fonts:
                            font_path = font_matcher.fonts[matched_font]
                            
                        # 使用真实路径检测日文支持
                        supports_japanese = False
                        if font_path and os.path.exists(font_path):
                            supports_japanese = translator.test_japanese_support(font_path)
                        
                        if supports_japanese:
                            print(f"     → {matched_font} (相似度: {font_confidence:.3f}, 支持日文)")
                            # 将真实路径添加到映射中，确保后续使用
                            translator.font_mapping[matched_font] = font_path
                            font_info.append({
                                'text': text,
                                'font': matched_font,
                                'confidence': font_confidence,
                                'region_id': j
                            })
                        else:
                            # 使用思源黑体作为默认日文支持字体兜底
                            fallback_font = '思源黑体'
                            vf_path = os.path.join('fonts', '思源黑体', 'SourceHanSans-VF.otf')
                            if os.path.exists(vf_path):
                                print(f"     → {matched_font} (相似度: {font_confidence:.3f}, 不支持日文) → 使用 {fallback_font}")
                                translator.font_mapping[fallback_font] = vf_path
                                font_info.append({
                                    'text': text,
                                    'font': fallback_font,
                                    'confidence': font_confidence,
                                    'region_id': j
                                })
                            else:
                                print(f"     → {matched_font} (相似度: {font_confidence:.3f}, 无日文支持且无兜底字体)")
                else:
                    other_count += 1
            
            # === 第一阶段：布局模式识别 ===
            print("\n" + "="*50)
            print("开始第一阶段：布局模式识别")
            print("="*50)
            
            # 初始化布局分析器
            layout_analyzer = LayoutAnalyzer()
            
            # 分析文本布局
            layout_result = layout_analyzer.analyze_text_layout(dt_polys, rec_texts, rec_scores)
            
            # 获取对齐策略建议
            alignment_strategies = layout_analyzer.suggest_alignment_strategy(layout_result)
            
            print(f"布局策略建议:")
            for strategy in alignment_strategies:
                print(f"  策略: {strategy['type']}")
                print(f"  描述: {strategy['description']}")
                print(f"  适用范围: {strategy['regions']}")
            
            print("="*50)
            print("第一阶段布局分析完成")
            print("="*50 + "\n")
                        
            # 生成可视化图片
            draw_ocr_result_simple(img_path, dt_polys, rec_texts, rec_scores, "output/detection_simple.png")
            
            # 生成布局分析调试图
            draw_layout_analysis_debug(img_path, layout_result, "output/layout_analysis_debug.png")
            
            # 预处理翻译区域以获取分组信息
            chinese_regions_data = [(poly, text, score) for poly, text, score in zip(dt_polys, rec_texts, rec_scores) if is_chinese_text(text) and score > 0.5]
            
            if chinese_regions_data:
                # 创建临时翻译区域数据
                temp_regions = []
                for i, (poly, text, score) in enumerate(chinese_regions_data):
                    x, y, w, h = cv2.boundingRect(np.array(poly, dtype=np.int32))
                    
                    # 使用与实际翻译相同的高度测量方法
                    roi = cv_image[y:y+h, x:x+w].copy()
                    real_h = translator.measure_roi_text_height(roi, prefix=f"debug_roi_{i}")
                    if real_h > 0:
                        h = real_h  # 用真实笔画高度替换
                    
                    translated_text = translator.translate_text(text)
                    
                    # 查找字体信息
                    region_font_info = None
                    for info in font_info:
                        if info['text'] == text:
                            region_font_info = info
                            break
                    
                    if region_font_info:
                        # 提取样式信息
                        style_info = translator.extract_text_style(cv_image, poly)
                        
                        temp_regions.append({
                            'bbox': (x, y, w, h),
                            'original_text': text,
                            'translated_text': translated_text,
                            'font_info': region_font_info,
                            'style_info': style_info
                        })
                
                # 应用布局感知分组策略
                processed_regions = translator.apply_layout_aware_strategy(temp_regions, cv_image, layout_result)
                
                # 生成分组信息调试图
                draw_grouping_debug(img_path, layout_result, processed_regions, "output/grouping_debug.png")
                
                # 生成对齐调试图
                draw_alignment_debug_with_groups(img_path, dt_polys, rec_texts, rec_scores, font_info, translator, processed_regions, "output/alignment_debug.png", layout_result)
            else:
                # 如果没有中文文字，使用原来的调试图
                draw_alignment_debug(img_path, dt_polys, rec_texts, rec_scores, font_info, translator, "output/alignment_debug.png", layout_result)
            
            # 只为中文文字创建掩码和进行去除操作
            if cv_image is not None and chinese_polys:
                # 创建中文文字掩码
                mask = np.zeros(cv_image.shape[:2], dtype=np.uint8)
                for poly in chinese_polys:
                    points = np.array(poly, dtype=np.int32)
                    cv2.fillPoly(mask, [points], 255)
                
                cv2.imwrite("output/chinese_text_mask.png", mask)
                
                # 移除中文文字
                text_removed_image = cv2.inpaint(cv_image, mask, 5, cv2.INPAINT_TELEA)
                cv2.imwrite("output/chinese_text_removed.png", text_removed_image)
                
                # 处理翻译和重绘
                chinese_regions_data = [(poly, text, score) for poly, text, score in zip(dt_polys, rec_texts, rec_scores) if is_chinese_text(text) and score > 0.5]
                
                if chinese_regions_data:
                    final_image, translation_log = translator.process_translation_results(
                        text_removed_image, 
                        chinese_regions_data, 
                        font_info,
                        cv_image,  # 传入原始图像用于样式提取
                        layout_result  # 传入布局分析结果
                    )
                    
                    # 保存最终翻译结果
                    cv2.imwrite("output/final_translated.png", final_image)
                    print(f"最终翻译结果已保存: output/final_translated.png")
                    
                    # 显示翻译日志
                    if translation_log:
                        print(f"\n翻译完成统计:")
                        for log in translation_log:
                            print(f"  '{log['original']}' → '{log['translated']}' (字体: {log['font']})")
                    else:
                        print("未找到可翻译的文字")
                else:
                    print("未找到有效的中文文字区域")
                    
            elif cv_image is not None and not chinese_polys:
                print("未检测到中文文字，跳过文字去除操作")
            else:
                print("无法读取图像文件")
        else:
            print("无法提取文字数据")
    
    print("处理完成!")

if __name__ == "__main__":
    # ===== 可调节参数区域 =====
    # 思源黑体粗细设置 (100-900):
    # 100=超细, 200=细, 300=较细, 400=正常, 500=中等, 
    # 600=半粗, 700=粗, 800=超粗, 900=黑体
    FONT_WEIGHT = 400  # 您可以修改这个值来调整所有译文的粗细
    # ========================
    
    print(f"当前字体粗细设置: {FONT_WEIGHT}")
    print("提示: 您可以修改 FONT_WEIGHT 值 (100-900) 来调整思源黑体的粗细")
    print("      100=超细, 400=正常, 700=粗, 900=黑体")
    print()
    
    try:
        process_image_with_latest_paddleocr(font_weight=FONT_WEIGHT)
    except Exception as e:
        print(f"程序执行失败: {e}")
    print("程序结束")
# Processors API 参考文档

本文档提供了所有处理器模块的详细API参考，包括方法签名、参数说明、返回值和异常处理。

## 通用数据结构

### ProcessingResult
所有处理器的统一返回类型：
```python
class ProcessingResult:
    success: bool           # 处理是否成功
    data: Any              # 处理结果数据
    error_message: str     # 错误信息（失败时）
    
    @classmethod
    def success_result(cls, data):
        """创建成功结果"""
        
    @classmethod
    def error_result(cls, error_message):
        """创建错误结果"""
```

---

## OCRProcessor API

### 类初始化
```python
class OCRProcessor:
    def __init__(self):
        """初始化OCR处理器"""
```

### 核心方法

#### process_image
```python
def process_image(
    self, 
    image_path: str, 
    confidence_threshold: float = 0.5
) -> ProcessingResult:
    """
    处理图像进行OCR识别
    
    Args:
        image_path: 图像文件路径
        confidence_threshold: 置信度阈值 (0.0-1.0)
        
    Returns:
        ProcessingResult[OCRResult]: OCR识别结果
        
    Raises:
        FileNotFoundError: 图像文件不存在
        ValueError: 图像格式不支持
        RuntimeError: OCR初始化失败
    """
```

#### extract_complete_style
```python
def extract_complete_style(
    self, 
    image: np.ndarray, 
    region_bbox: tuple
) -> dict:
    """
    提取文字区域的完整样式信息
    
    Args:
        image: 原始图像 (H, W, 3)
        region_bbox: 区域边界框 (x, y, w, h)
        
    Returns:
        dict: 样式信息字典
        {
            'estimated_font_size': int,    # 估算字体大小
            'precise_height': int,         # 精确高度
            'text_color': tuple,           # 文字颜色 (R, G, B)
            'background_color': tuple,     # 背景颜色 (R, G, B)
            'is_bold': bool,               # 是否粗体
            'is_dark_text': bool,          # 是否深色文字
            'contrast_ratio': float,       # 对比度比率
            'region_width': int,           # 区域宽度
            'region_height': int           # 区域高度
        }
    """
```

#### measure_roi_text_height
```python
def measure_roi_text_height(
    self, 
    roi_bgr: np.ndarray, 
    debug_prefix: Optional[str] = None
) -> int:
    """
    测量文字区域的精确高度
    
    Args:
        roi_bgr: 文字区域图像 (H, W, 3)
        debug_prefix: 调试文件前缀
        
    Returns:
        int: 文字实际高度（像素）
    """
```

#### extract_colors_advanced
```python
def extract_colors_advanced(
    self, 
    text_region: np.ndarray
) -> tuple:
    """
    高级颜色提取算法
    
    Args:
        text_region: 文字区域图像
        
    Returns:
        tuple: (text_color, bg_color, is_dark_text)
            text_color: 文字颜色 (R, G, B)
            bg_color: 背景颜色 (R, G, B)
            is_dark_text: 是否深色文字
    """
```

#### is_chinese_text
```python
def is_chinese_text(self, text: str) -> bool:
    """
    判断文本是否包含中文字符
    
    Args:
        text: 待检测文本
        
    Returns:
        bool: 是否包含中文
    """
```

#### cleanup
```python
def cleanup(self):
    """清理OCR资源"""
```

---

## LayoutProcessor API

### 类初始化
```python
class LayoutProcessor:
    def __init__(self):
        """初始化布局处理器"""
```

### 核心方法

#### process_layout
```python
def process_layout(
    self, 
    chinese_regions: List[TextRegion]
) -> ProcessingResult:
    """
    分析文字区域的布局关系
    
    Args:
        chinese_regions: 中文文字区域列表
        
    Returns:
        ProcessingResult[LayoutResult]: 布局分析结果
    """
```

### 布局分析方法

#### detect_horizontal_alignment
```python
def _detect_horizontal_alignment(
    self, 
    regions_data: List[dict]
) -> dict:
    """
    检测水平对齐关系
    
    Returns:
        dict: 对齐信息
        {
            'left_groups': List[List[dict]],      # 左对齐组
            'center_groups': List[List[dict]],    # 居中对齐组
            'right_groups': List[List[dict]],     # 右对齐组
            'distribution_groups': List[List[dict]] # 分布组
        }
    """
```

#### analyze_vertical_distribution
```python
def _analyze_vertical_distribution(
    self, 
    regions_data: List[dict]
) -> dict:
    """
    分析垂直分布
    
    Returns:
        dict: 垂直分布信息
        {
            'type': str,                    # 分布类型
            'row_count': int,               # 行数
            'rows': List[List[dict]],       # 行分组
            'row_spacings': List[int]       # 行间距
        }
    """
```

#### analyze_spacing_pattern
```python
def _analyze_spacing_pattern(
    self, 
    regions_data: List[dict]
) -> dict:
    """
    分析间距模式
    
    Returns:
        dict: 间距模式信息
        {
            'type': str,                      # 间距类型
            'horizontal_regularity': str,     # 水平规律性
            'vertical_regularity': str,       # 垂直规律性
            'horizontal_spacings': List[int], # 水平间距
            'vertical_spacings': List[int]    # 垂直间距
        }
    """
```

---

## FontProcessor API

### 类初始化
```python
class FontProcessor:
    def __init__(self):
        """初始化字体处理器"""
```

### 核心方法

#### process_regions
```python
def process_regions(
    self, 
    image: np.ndarray, 
    regions: List[TextRegion]
) -> ProcessingResult:
    """
    处理文字区域进行字体匹配
    
    Args:
        image: 原始图像 (H, W, 3)
        regions: 文字区域列表
        
    Returns:
        ProcessingResult[List[FontMatchResult]]: 字体匹配结果列表
    """
```

#### test_japanese_support
```python
def test_japanese_support(self, font_path: str) -> bool:
    """
    测试字体是否支持日文字符
    
    Args:
        font_path: 字体文件路径
        
    Returns:
        bool: 是否支持日文
        
    Raises:
        FileNotFoundError: 字体文件不存在
        ValueError: 字体文件格式错误
    """
```

### 内部方法

#### _match_font
```python
def _match_font(
    self, 
    text_region: np.ndarray, 
    text: str
) -> Tuple[str, float]:
    """
    匹配最相似的字体
    
    Args:
        text_region: 文字区域图像
        text: 文字内容
        
    Returns:
        Tuple[str, float]: (字体名称, 相似度)
    """
```

#### _calculate_similarity
```python
def _calculate_similarity(
    self, 
    region1: np.ndarray, 
    region2: np.ndarray
) -> float:
    """
    计算两个图像区域的相似度
    
    Args:
        region1: 第一个图像区域
        region2: 第二个图像区域
        
    Returns:
        float: 相似度 (0.0-1.0)
    """
```

---

## TranslationProcessor API

### 类初始化
```python
class TranslationProcessor:
    def __init__(self, weight_adjustment: int = 400):
        """
        初始化翻译处理器
        
        Args:
            weight_adjustment: 可变字体粗细调节值 (100-900)
        """
```

### 核心方法

#### process_translation
```python
def process_translation(
    self,
    image: np.ndarray,
    regions: List[TextRegion],
    font_results: List[FontMatchResult],
    layout_result: LayoutResult
) -> ProcessingResult:
    """
    处理翻译任务
    
    Args:
        image: 原始图像 (H, W, 3)
        regions: 文字区域列表
        font_results: 字体匹配结果列表
        layout_result: 布局分析结果
        
    Returns:
        ProcessingResult[List[TranslationResult]]: 翻译结果列表
    """
```

#### translate_text
```python
def translate_text(self, text: str) -> str:
    """
    翻译单个文本
    
    Args:
        text: 待翻译文本
        
    Returns:
        str: 翻译结果
    """
```

#### calculate_font_size_by_height_matching
```python
def _calculate_font_size_by_height_matching(
    self,
    text: str,
    target_height: int,
    font_path: str
) -> int:
    """
    通过高度匹配计算字体大小
    
    Args:
        text: 文字内容
        target_height: 目标高度
        font_path: 字体路径
        
    Returns:
        int: 计算得到的字体大小
    """
```

---

## InpaintProcessor API

### 类初始化
```python
class InpaintProcessor:
    def __init__(self):
        """初始化图像修复处理器"""
```

### 核心方法

#### process_inpainting
```python
def process_inpainting(
    self,
    image: np.ndarray,
    chinese_regions: List[TextRegion],
    save_debug: bool = True
) -> ProcessingResult:
    """
    处理图像修复，去除中文文字
    
    Args:
        image: 原始图像 (H, W, 3)
        chinese_regions: 中文文字区域列表
        save_debug: 是否保存调试图像
        
    Returns:
        ProcessingResult[np.ndarray]: 修复后的图像
    """
```

#### inpaint_with_different_methods
```python
def inpaint_with_different_methods(
    self,
    image: np.ndarray,
    mask: np.ndarray,
    method: str = "telea"
) -> np.ndarray:
    """
    使用不同方法进行图像修复
    
    Args:
        image: 原始图像 (H, W, 3)
        mask: 掩码图像 (H, W)
        method: 修复方法 ("telea", "ns")
        
    Returns:
        np.ndarray: 修复后的图像
        
    Raises:
        ValueError: 不支持的修复方法
    """
```

### 内部方法

#### _create_text_mask
```python
def _create_text_mask(
    self,
    image: np.ndarray,
    regions: List[TextRegion]
) -> np.ndarray:
    """
    创建文字掩码
    
    Args:
        image: 原始图像
        regions: 文字区域列表
        
    Returns:
        np.ndarray: 掩码图像 (H, W)
    """
```

---

## Renderer API

### 类初始化
```python
class Renderer:
    def __init__(self, weight_adjustment: int = 400):
        """
        初始化渲染器
        
        Args:
            weight_adjustment: 可变字体粗细调节值 (100-900)
        """
```

### 核心方法

#### render_translations
```python
def render_translations(
    self,
    base_image: np.ndarray,
    translation_results: List[TranslationResult],
    layout_result: LayoutResult
) -> ProcessingResult:
    """
    渲染翻译文字到图像上
    
    Args:
        base_image: 基础图像（已去除原文字） (H, W, 3)
        translation_results: 翻译结果列表
        layout_result: 布局分析结果
        
    Returns:
        ProcessingResult[dict]: 渲染结果
        {
            'image': np.ndarray,        # 最终图像
            'render_log': List[dict]    # 渲染日志
        }
    """
```

#### calculate_optimal_font_size
```python
def calculate_optimal_font_size(
    self,
    text: str,
    target_width: int,
    target_height: int,
    font_path: str
) -> int:
    """
    计算最优字体大小
    
    Args:
        text: 文字内容
        target_width: 目标宽度
        target_height: 目标高度
        font_path: 字体路径
        
    Returns:
        int: 最优字体大小
    """
```

### 内部方法

#### _calculate_render_configs
```python
def _calculate_render_configs(
    self,
    translation_results: List[TranslationResult],
    layout_result: LayoutResult
) -> List[RenderConfig]:
    """
    计算渲染配置
    
    Returns:
        List[RenderConfig]: 渲染配置列表
    """
```

#### _render_single_text
```python
def _render_single_text(
    self,
    draw: ImageDraw.Draw,
    result: TranslationResult,
    config: RenderConfig
):
    """
    渲染单个文字
    
    Args:
        draw: PIL绘制对象
        result: 翻译结果
        config: 渲染配置
    """
```

---

## 异常处理

### 常见异常类型

#### OCRProcessor
- `FileNotFoundError`: 图像文件不存在
- `ValueError`: 图像格式不支持或参数无效
- `RuntimeError`: PaddleOCR初始化失败

#### FontProcessor
- `FileNotFoundError`: 字体文件不存在
- `ValueError`: 字体文件格式错误
- `ImportError`: 缺少必要的字体处理库

#### TranslationProcessor
- `KeyError`: 翻译字典中缺少对应翻译
- `ValueError`: 字体大小计算失败

#### InpaintProcessor
- `ValueError`: 图像尺寸不匹配或修复方法不支持

#### Renderer
- `FileNotFoundError`: 字体文件不存在
- `ValueError`: 渲染参数无效
- `MemoryError`: 图像过大导致内存不足

### 异常处理示例

```python
try:
    result = processor.process_something(...)
    if result.success:
        data = result.data
        # 处理成功逻辑
    else:
        print(f"处理失败: {result.error_message}")
except FileNotFoundError as e:
    print(f"文件不存在: {e}")
except ValueError as e:
    print(f"参数错误: {e}")
except Exception as e:
    print(f"未知错误: {e}")
```

---

## 性能注意事项

### 内存管理
- 及时释放大图像对象
- 使用适当的图像尺寸
- 避免在循环中创建大量临时对象

### 缓存机制
- 字体大小计算结果会被缓存
- OCR实例会被复用
- 字体文件加载会被缓存

### 并发安全
- 所有处理器都是线程安全的
- 可以并行处理不同的图像
- 避免同时修改共享配置

---

本API参考文档提供了所有处理器的详细接口信息。如需了解具体实现细节，请参考源代码和使用文档。

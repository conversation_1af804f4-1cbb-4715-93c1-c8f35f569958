from PIL import Image, ImageDraw, ImageFont
import cv2
import numpy as np
import os

class SimpleTranslator:
    def __init__(self, weight_adjustment=400):
        """
        初始化翻译器
        
        参数:
        weight_adjustment: 可变字体粗细调节值 (100-900, 默认400为正常)
                          100=超细, 200=细, 300=较细, 400=正常, 500=中等, 
                          600=半粗, 700=粗, 800=超粗, 900=黑体
        """
        # 加载翻译字典
        self.translation_dict = {}
        self.weight_adjustment = weight_adjustment  # 添加粗细调节参数
        
        # 简单的中日文对照字典
        self.translation_dict = {
            "护脊靠枕": "背骨サポートまくら",
            "呵护爱宠脊椎": "大切なペットの背骨ケア",
            "4D高回弹记忆棉": "4D高反発記憶フォーム",
            "久睡不塌": "長時間でも沈まない",
            "适用更久": "長く使える",
            '"0压感"': "ゼロ圧感",
            "0压感": "ゼロ圧感",
            "高回弹海绵": "高反発スポンジ",
            "深度分散压力": "圧力を分散",
            "不易塌陷": "へたりにくい"
        }
        
        # 扩展的字体映射 - 只使用本地项目字体
        base_dir = os.path.dirname(os.path.dirname(__file__))  # 从core目录向上一级到项目根目录
        fonts_dir = os.path.join(base_dir, 'fonts')
        
        self.font_mapping = {
            # 本地字体映射
            'Noto Sans SC Black': os.path.join(fonts_dir, 'NotoSansSC', 'NotoSansSC-Black.ttf'),
            'Spoqa Han Sans Bold': os.path.join(fonts_dir, 'SpoqaHanSans', 'SpoqaHanSansBold.ttf'),
            '台北黑体 TC Bold': os.path.join(fonts_dir, '台北黑体', 'TaipeiSans-Bold.ttf'),
            '思源黑体': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            
            # 通用字体名称映射到本地字体
            '宋体': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            '黑体': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            '楷体': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            '微软雅黑': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            '仿宋': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            '隶书': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            '幼圆': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            
            # 高质量字体映射
            'MS Gothic': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            'MS Mincho': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            'Yu Gothic': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            'Microsoft JhengHei': os.path.join(fonts_dir, '台北黑体', 'TaipeiSans-Bold.ttf'),
            '华文黑体': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            '华文楷体': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            '华文宋体': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            
            # 英文字体映射（支持CJK）
            'Arial': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            'Times New Roman': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            'Calibri': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            'Segoe UI': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            
            # 现代字体
            'Noto Sans': os.path.join(fonts_dir, 'NotoSansSC', 'NotoSansSC-Black.ttf'),
            '思源宋体': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf'),
            '霞鹜文楷': os.path.join(fonts_dir, '思源黑体', 'SourceHanSans-VF.otf')
        }
        
        # 过滤掉不存在的字体文件
        valid_font_mapping = {}
        for font_name, font_path in self.font_mapping.items():
            if os.path.exists(font_path):
                valid_font_mapping[font_name] = font_path
            else:
                print(f"警告：字体文件不存在 {font_path}，跳过字体 {font_name}")
        
        self.font_mapping = valid_font_mapping
        
        print(f"翻译字典已加载: {len(self.translation_dict)} 条记录")
        print(f"可用字体映射: {list(self.font_mapping.keys())}")

    
    def test_japanese_support(self, font_path):
        """测试字体是否支持日文字符"""
        try:
            font = ImageFont.truetype(font_path, 16)
            # 测试常见的日文字符
            test_chars = ['あ', 'か', 'サ', 'ポ', '大', '切']
            
            for char in test_chars:
                bbox = font.getbbox(char)
                width = bbox[2] - bbox[0]
                # 如果字符宽度太小，可能不支持
                if width < 8:
                    return False
            
            return True
        except:
            return False
    
    def translate_text(self, chinese_text):
        """翻译中文文本为日文"""
        # 精确匹配
        if chinese_text in self.translation_dict:
            return self.translation_dict[chinese_text]
        
        # 模糊匹配 - 检查是否包含字典中的关键词
        for cn_key, jp_value in self.translation_dict.items():
            if cn_key in chinese_text or chinese_text in cn_key:
                return jp_value
        
        # 如果没有匹配，返回原文
        return chinese_text
    
    def calculate_font_size(self, text, region_width, region_height, font_path):
        """根据区域大小计算合适的字体大小"""
        if not font_path:
            return 20
        
        # 从较大的字体开始尝试
        for size in range(50, 10, -2):
            try:
                font = ImageFont.truetype(font_path, size)
                bbox = font.getbbox(text)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                
                # 如果文字能放入区域（留一些边距）
                if text_width <= region_width * 0.9 and text_height <= region_height * 0.9:
                    return size
            except:
                continue
        
        return 16  # 最小字体大小
    
    def extract_text_style(self, original_image, region_poly):
        """提取原始文字的样式特征"""
        points = np.array(region_poly, dtype=np.int32)
        x, y, w, h = cv2.boundingRect(points)
        
        # 提取文字区域
        text_region = original_image[y:y+h, x:x+w]
        
        # 转换为灰度图
        if len(text_region.shape) == 3:
            gray_region = cv2.cvtColor(text_region, cv2.COLOR_BGR2GRAY)
        else:
            gray_region = text_region
        
        # 改进的颜色分析 - 支持全彩色检测
        text_color, bg_color, is_dark_text = self.extract_colors_advanced(text_region)
        
        # 估算原始字体大小 - 提高估算精度
        text_height = h
        text_width = w
        # 根据文字区域的实际尺寸更准确地估算字体大小
        # 使用文字区域高度作为主要参考，考虑padding
        estimated_font_size = int(text_height * 0.95)  # 提高系数，让字体更接近原始大小
        

        
        # 分析文字粗细 - 需要二值化图像
        if len(text_region.shape) == 3:
            gray_for_bold = cv2.cvtColor(text_region, cv2.COLOR_BGR2GRAY)
        else:
            gray_for_bold = text_region
        
        _, binary = cv2.threshold(gray_for_bold, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 改进的粗体分析算法
        kernel = np.ones((2,2), np.uint8)  # 使用更小的核
        eroded = cv2.erode(binary, kernel, iterations=1)
        
        # 计算笔画密度
        text_pixels = np.sum(binary == 0)  # 黑色文字像素
        total_pixels = w * h
        fill_ratio = text_pixels / total_pixels
        
        # 计算笔画厚度 - 通过腐蚀前后的比较
        eroded_pixels = np.sum(eroded == 0)
        thickness_retention = eroded_pixels / max(text_pixels, 1)
        
        # 更严格的粗体判断 - 提高阈值，减少误判
        is_bold = (fill_ratio > 0.25 and thickness_retention > 0.6) or fill_ratio > 0.4
        

        
        style_info = {
            'text_color': text_color,
            'bg_color': bg_color,
            'estimated_font_size': estimated_font_size,
            'is_bold': is_bold,
            'is_dark_text': is_dark_text,
            'region_width': w,
            'region_height': h
        }
        
        return style_info
    
    def extract_colors_advanced(self, text_region):
        """改进的颜色提取算法，支持全彩色检测"""
        h, w = text_region.shape[:2]
        
        if len(text_region.shape) == 3:
            # 彩色图像处理
            
            # 方法1：基于边缘检测找文字轮廓
            gray = cv2.cvtColor(text_region, cv2.COLOR_BGR2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            
            # 方法2：基于颜色聚类
            pixels = text_region.reshape((-1, 3))
            from sklearn.cluster import KMeans
            
            try:
                # 使用K-means聚类找主要颜色
                kmeans = KMeans(n_clusters=2, random_state=42, n_init=10)
                kmeans.fit(pixels)
                colors = kmeans.cluster_centers_
                labels = kmeans.labels_
                
                # 计算每个簇的像素数量
                unique, counts = np.unique(labels, return_counts=True)
                
                # 假设数量少的是文字，数量多的是背景
                if counts[0] < counts[1]:
                    text_color = tuple(map(int, colors[0]))
                    bg_color = tuple(map(int, colors[1]))
                else:
                    text_color = tuple(map(int, colors[1]))
                    bg_color = tuple(map(int, colors[0]))
                
                # 判断是否为深色文字
                text_brightness = np.mean(text_color)
                bg_brightness = np.mean(bg_color)
                is_dark_text = text_brightness < bg_brightness
                
            except:
                # 聚类失败时使用备选方案
                text_color, bg_color, is_dark_text = self.extract_colors_fallback(text_region)
        
        else:
            # 灰度图像
            text_color, bg_color, is_dark_text = self.extract_colors_fallback(text_region)
        
        return text_color, bg_color, is_dark_text
    
    def extract_colors_fallback(self, text_region):
        """备选颜色提取方法"""
        if len(text_region.shape) == 3:
            gray = cv2.cvtColor(text_region, cv2.COLOR_BGR2GRAY)
        else:
            gray = text_region
        
        # 使用OTSU二值化
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        black_pixels = np.sum(binary == 0)
        white_pixels = np.sum(binary == 255)
        
        if black_pixels < white_pixels:
            text_mask = (binary == 0)
            is_dark_text = True
        else:
            text_mask = (binary == 255)
            is_dark_text = False
        
        if len(text_region.shape) == 3:
            # 提取真实颜色
            text_pixels = text_region[text_mask]
            bg_pixels = text_region[~text_mask]
            
            if len(text_pixels) > 0:
                text_color = tuple(map(int, np.mean(text_pixels, axis=0)))
            else:
                text_color = (0, 0, 0) if is_dark_text else (255, 255, 255)
            
            if len(bg_pixels) > 0:
                bg_color = tuple(map(int, np.mean(bg_pixels, axis=0)))
            else:
                bg_color = (255, 255, 255) if is_dark_text else (0, 0, 0)
        else:
            text_color = (0, 0, 0) if is_dark_text else (255, 255, 255)
            bg_color = (255, 255, 255) if is_dark_text else (0, 0, 0)
        
        return text_color, bg_color, is_dark_text
    
    def draw_bold_text(self, draw, text, x, y, font, color):
        """改进的粗体文字绘制方法"""
        # 方法1：尝试获取粗体字体
        try:
            font_path = font.path if hasattr(font, 'path') else None
            if font_path:
                # 尝试找到对应的粗体字体文件
                bold_font_path = self.find_bold_font(font_path)
                if bold_font_path:
                    bold_font = ImageFont.truetype(bold_font_path, font.size)
                    draw.text((x, y), text, font=bold_font, fill=color)
                    return
        except:
            pass
        
        # 方法2：轻度粗体模拟效果（减少偏移）
        # 只使用必要的偏移来创建自然的粗体效果
        offsets = [
            # 主要方向 - 减少偏移距离
            (-0.5, 0), (0.5, 0), (0, -0.5), (0, 0.5),
            # 对角线方向 - 仅用于平滑
            (-0.5, -0.5), (0.5, 0.5)
        ]
        
        # 绘制轻微偏移创建自然粗体效果
        for dx, dy in offsets:
            draw.text((x + dx, y + dy), text, font=font, fill=color)
        
        # 主文字层
        draw.text((x, y), text, font=font, fill=color)
    
    def find_bold_font(self, regular_font_path):
        """尝试找到对应的粗体字体文件"""
        import os
        
        font_dir = os.path.dirname(regular_font_path)
        font_name = os.path.basename(regular_font_path)
        
        # 常见的粗体字体后缀
        bold_patterns = [
            '_Bold', 'Bold', '_B', 'B', '_Heavy', 'Heavy',
            '_Black', 'Black', '_Semibold', 'Semibold'
        ]
        
        for pattern in bold_patterns:
            # 尝试不同的粗体命名模式
            name_without_ext = os.path.splitext(font_name)[0]
            ext = os.path.splitext(font_name)[1]
            
            bold_candidates = [
                os.path.join(font_dir, name_without_ext + pattern + ext),
                os.path.join(font_dir, name_without_ext + '-' + pattern + ext),
                os.path.join(font_dir, name_without_ext.replace('Regular', pattern) + ext),
                os.path.join(font_dir, name_without_ext.replace('Normal', pattern) + ext)
            ]
            
            for candidate in bold_candidates:
                if os.path.exists(candidate):
                    return candidate
        
        return None
    
    def draw_text_on_image(self, image, text, region_poly, font_name, font_confidence, original_image):
        """在图像上绘制翻译后的文字，保持原始样式"""
        # 提取原始文字样式
        style_info = self.extract_text_style(original_image, region_poly)
        
        # 获取区域边界框
        points = np.array(region_poly, dtype=np.int32)
        x, y, w, h = cv2.boundingRect(points)
        
        # 获取对应的日文字体
        font_path = self.font_mapping.get(font_name)
        if not font_path:
            print(f"警告: 未找到字体 {font_name} 的日文对应字体，使用默认字体")
        
        # 使用提取的字体大小作为起始点，应用高度匹配策略
        base_font_size = max(style_info['estimated_font_size'], 18)  # 提高最小字体大小
        
        print(f"    开始高度匹配调整 - 原始文字高度: {h}px, 估算字体: {base_font_size}px")
        
        # 应用新的高度匹配算法
        font_size = self.calculate_font_size_with_style(text, w, h, font_path, base_font_size)
        
        print(f"    高度匹配完成: {base_font_size} → {font_size}px")
        
        # 转换为PIL图像进行文字绘制
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_image)
        
        try:
            if font_path:
                font = ImageFont.truetype(font_path, font_size)
            else:
                font = ImageFont.load_default()
        except:
            font = ImageFont.load_default()
            print(f"字体加载失败，使用默认字体")
        
        # 计算文字位置（居中）
        bbox = font.getbbox(text)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # 在区域中心绘制文字，确保不超出图像边界
        text_x = max(0, x + (w - text_width) // 2)
        text_y = max(0, y + (h - text_height) // 2)
        
        # 确保文字不会超出图像右边和底部
        image_height, image_width = image.shape[:2]
        text_x = min(text_x, image_width - text_width - 10)
        text_y = min(text_y, image_height - text_height - 10)
        
        # 转换颜色格式 (BGR -> RGB)
        if len(style_info['text_color']) == 3:
            text_color_rgb = (style_info['text_color'][2], style_info['text_color'][1], style_info['text_color'][0])
            bg_color_rgb = (style_info['bg_color'][2], style_info['bg_color'][1], style_info['bg_color'][0])
        else:
            text_color_rgb = style_info['text_color']
            bg_color_rgb = style_info['bg_color']
        
        # 不绘制背景，让文字直接覆盖在原图上
        # background_margin = 3
        # draw.rectangle([
        #     text_x - background_margin,
        #     text_y - background_margin,
        #     text_x + text_width + background_margin,
        #     text_y + text_height + background_margin
        # ], fill=bg_color_rgb, outline=bg_color_rgb)
        
        # 改进的粗体渲染
        if style_info['is_bold']:
            self.draw_bold_text(draw, text, text_x, text_y, font, text_color_rgb)
        else:
            # 普通文字
            draw.text((text_x, text_y), text, font=font, fill=text_color_rgb)
        
        # 转换回OpenCV格式
        result_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        
        print(f"已绘制翻译文字: '{text}' 在位置 ({text_x}, {text_y}) 字体大小: {font_size}")
        print(f"  样式: 颜色{text_color_rgb}, 粗体: {style_info['is_bold']}")
        
        return result_image
    
    def text_fits_in_region(self, text, font_path, font_size, region_width, region_height):
        """检查文字是否适合指定区域"""
        try:
            if font_path and os.path.exists(font_path):
                font = ImageFont.truetype(font_path, font_size)
            else:
                font = ImageFont.load_default()
            
            bbox = font.getbbox(text)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # 允许一定的宽松度
            return text_width <= region_width * 1.1 and text_height <= region_height * 1.1
        except:
            return False

    def measure_rendered_text_height(self, text, font_path, font_size, sample_width=200, sample_height=100):
        """在实际图像中渲染文字并测量其真实像素高度"""
        try:
            # 创建测试画布
            test_image = Image.new('RGB', (sample_width, sample_height), color=(255, 255, 255))
            draw = ImageDraw.Draw(test_image)
            
            # 加载字体并渲染文字
            if self.is_variable_font(font_path):
                font = self.create_variable_font(font_path, font_size)
            else:
                font = ImageFont.truetype(font_path, font_size)
            
            # 在画布中心渲染文字
            text_x = sample_width // 4
            text_y = sample_height // 4
            draw.text((text_x, text_y), text, font=font, fill=(0, 0, 0))
            
            # 转换为numpy数组进行像素分析
            img_array = np.array(test_image)
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            
            # 二值化找到文字像素
            _, binary = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY_INV)
            
            # 找到文字区域的边界框
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if contours:
                # 获取最大轮廓（应该是我们的文字）
                largest_contour = max(contours, key=cv2.contourArea)
                x, y, w, h = cv2.boundingRect(largest_contour)
                return h
            else:
                # 如果找不到轮廓，回退到字体度量
                bbox = font.getbbox(text)
                return bbox[3] - bbox[1]
                
        except Exception as e:
            # 出错时回退到字体度量
            try:
                font = ImageFont.truetype(font_path, font_size)
                bbox = font.getbbox(text)
                return bbox[3] - bbox[1]
            except:
                return font_size
    
    def calculate_font_size_by_height_matching(self, text, target_height, font_path, initial_guess=None):
        """通过实际渲染高度匹配来计算字体大小"""
        if not font_path or not os.path.exists(font_path):
            return initial_guess or 20
        
        print(f"    开始像素级高度匹配: 目标高度{target_height}px")
        
        # 设置搜索范围
        min_size, max_size = 8, 100
        target_tolerance = 2  # 允许的高度误差（像素）
        
        # 如果有初始估计，从该点开始二分搜索
        if initial_guess:
            test_size = initial_guess
        else:
            test_size = (min_size + max_size) // 2
        

        
        best_size = test_size
        best_diff = float('inf')
        iteration = 0
        max_iterations = 12  # 避免无限循环
        
        while iteration < max_iterations:
            try:
                # 使用实际渲染测量高度
                actual_height = self.measure_rendered_text_height(text, font_path, test_size)
                height_diff = abs(actual_height - target_height)
                
                print(f"      字体{test_size}px: 渲染高度{actual_height}px vs 目标{target_height}px (差值{height_diff})")
                
                # 记录最佳匹配
                if height_diff < best_diff:
                    best_diff = height_diff
                    best_size = test_size
                
                # 如果差值在容忍范围内，直接返回
                if height_diff <= target_tolerance:
                    print(f"      ✓ 找到精确匹配: {test_size}px")
                    return test_size
                
                # 二分搜索调整
                if actual_height < target_height:
                    # 实际高度太小，需要增大字体
                    min_size = test_size + 1
                else:
                    # 实际高度太大，需要减小字体
                    max_size = test_size - 1
                
                # 检查搜索范围是否有效
                if min_size > max_size:
                    break
                
                test_size = (min_size + max_size) // 2
                iteration += 1
                
            except Exception as e:
                print(f"      渲染测试失败: {e}")
                break
        
        print(f"      最终选择: {best_size}px (渲染高度差{best_diff:.1f}px)")
        return best_size

    def calculate_font_size_with_style(self, text, region_width, region_height, font_path, base_size):
        """只根据高度匹配计算字体大小"""
        if not font_path:
            return min(base_size, 24)
        
        # 只使用高度匹配算法，不考虑宽度约束
        height_matched_size = self.calculate_font_size_by_height_matching(
            text, region_height, font_path, base_size
        )
        
        return height_matched_size
    
    def add_translation(self, chinese, japanese):
        """添加新的翻译对"""
        self.translation_dict[chinese] = japanese
        print(f"已添加翻译: {chinese} → {japanese}")
    
    def get_all_translations(self):
        """获取所有翻译对"""
        return self.translation_dict.copy()
    
    def process_translation_results(self, image, chinese_regions, font_info, original_image, layout_result=None):
        """处理所有需要翻译的区域，使用多级降级策略避免重叠"""
        result_image = image.copy()
        translation_log = []
        
        # 第一阶段：准备所有翻译区域数据
        translation_regions = []
        for i, (poly, text, score) in enumerate(chinese_regions):
            # 查找对应的字体信息
            region_font_info = None
            for info in font_info:
                if info['text'] == text:
                    region_font_info = info
                    break
            
            if not region_font_info:
                print(f"警告: 未找到文字 '{text}' 的字体信息")
                continue
            
            # 翻译文字
            translated_text = self.translate_text(text)
            
            if translated_text != text:  # 如果有翻译结果
                print(f"'{text}' → '{translated_text}' (原文{len(text)}字符, 译文{len(translated_text)}字符)")
                
                # 提取样式信息
                style_info = self.extract_text_style(original_image, poly)
                points = np.array(poly, dtype=np.int32)
                x, y, w, h = cv2.boundingRect(points)
                # 计算ROI真实高度
                roi = original_image[y:y+h, x:x+w].copy()
                real_h = self.measure_roi_text_height(roi, prefix=f"roi_{i}")
                if real_h > 0:
                    h = real_h  # 用真实笔画高度替换
                
                translation_regions.append({
                    'id': i,
                    'original_text': text,
                    'translated_text': translated_text,
                    'poly': poly,
                    'bbox': (x, y, w, h),
                    'font_info': region_font_info,
                    'style_info': style_info,
                    'score': score
                })
        
        # 第二阶段：使用布局感知策略处理所有区域
        optimized_regions = self.apply_layout_aware_strategy(translation_regions, original_image, layout_result)
        
        # 第三阶段：绘制优化后的文字
        for region in optimized_regions:
            result_image = self.draw_optimized_text(
                result_image, 
                region,
                original_image
            )
            
            translation_log.append({
                'original': region['original_text'],
                'translated': region['translated_text'],
                'font': region['font_info']['font'],
                'confidence': region['font_info']['confidence'],
                'adjustments': region.get('adjustments', [])
            })
        
        return result_image, translation_log
    
    def apply_layout_aware_strategy(self, translation_regions, original_image, layout_result):
        """应用布局感知策略进行翻译"""
        print(f"\n开始布局感知处理...")
        
        # 第一步：布局感知分组
        grouped_regions = self.group_regions_with_layout_constraints(translation_regions, layout_result)
        
        # 第二步：为每组计算统一的字体约束
        for group_key, group_regions in grouped_regions.items():
            self.apply_unified_constraints_to_group(group_regions, group_key)
        
        # 第三步：为每个区域计算最终布局
        all_regions = []
        for group_regions in grouped_regions.values():
            for region in group_regions:
                self.calculate_layout_aware_layout(region, original_image, layout_result)
                all_regions.append(region)
        
        return all_regions
    
    def group_regions_with_layout_constraints(self, translation_regions, layout_result):
        """基于位置优先+高度容忍的智能分组"""
        print(f"开始智能分组（位置优先+高度容忍）...")
        
        # 第一步：识别水平行
        horizontal_lines = self.identify_horizontal_lines(translation_regions, y_tolerance=20)
        
        groups = {}
        group_counter = 1
        
        # 第二步：处理每个水平行
        for line_idx, line_regions in enumerate(horizontal_lines):
            print(f"\n处理水平行{line_idx + 1}: {len(line_regions)}个文字")
            
            if len(line_regions) == 1:
                # 单独文字，直接分组
                region = line_regions[0]
                height = self.normalize_height_tolerant(region['bbox'][3], tolerance=5)
                group_key = f"line{line_idx + 1}_h{height}_single"
                groups[group_key] = line_regions
                print(f"  '{region['original_text']}' → 单独分组: {group_key}")
                
            else:
                # 多个文字在同一行，按高度分组（使用容忍度）
                height_subgroups = self.group_by_height_tolerant(line_regions, tolerance=5)
                
                for height, height_regions in height_subgroups.items():
                    if len(height_regions) == len(line_regions):
                        # 所有文字高度相同，统一分组
                        group_key = f"line{line_idx + 1}_h{height}_unified"
                        groups[group_key] = height_regions
                        region_names = [r['original_text'] for r in height_regions]
                        print(f"  {region_names} → 统一分组: {group_key}")
                    else:
                        # 高度不同，分别分组
                        group_key = f"line{line_idx + 1}_h{height}_partial"
                        groups[group_key] = height_regions
                        region_names = [r['original_text'] for r in height_regions]
                        print(f"  {region_names} → 部分分组: {group_key}")
        
        print(f"\n智能分组完成，共{len(groups)}个组:")
        for group_key, group_regions in groups.items():
            region_names = [r['original_text'] for r in group_regions]
            print(f"  {group_key}: {region_names}")
        
        return groups
    
    def identify_horizontal_lines(self, regions, y_tolerance=20):
        """识别水平排列的文字行"""
        if not regions:
            return []
        
        # 按Y坐标排序
        regions_by_y = sorted(regions, key=lambda r: r['bbox'][1])
        
        horizontal_lines = []
        current_line = [regions_by_y[0]]
        
        for region in regions_by_y[1:]:
            prev_y = current_line[-1]['bbox'][1]
            curr_y = region['bbox'][1]
            
            # 如果Y坐标差异小于阈值，认为在同一水平线
            if abs(curr_y - prev_y) <= y_tolerance:
                current_line.append(region)
            else:
                # 当前行结束，开始新行
                horizontal_lines.append(current_line)
                current_line = [region]
        
        # 添加最后一行
        horizontal_lines.append(current_line)
        
        # 对每行内的文字按X坐标排序
        for line in horizontal_lines:
            line.sort(key=lambda r: r['bbox'][0])
        
        return horizontal_lines
    
    def normalize_height_tolerant(self, height, tolerance=5):
        """高度标准化，使用容忍度"""
        return round(height / tolerance) * tolerance
    
    def group_by_height_tolerant(self, regions, tolerance=5):
        """按高度分组，使用容忍度"""
        groups = {}
        
        for region in regions:
            height = region['bbox'][3]
            normalized = self.normalize_height_tolerant(height, tolerance)
            
            if normalized not in groups:
                groups[normalized] = []
            groups[normalized].append(region)
        
        return groups
    
    def normalize_height(self, region, tolerance=2):
        """标准化高度：±2像素容忍，统一为偶数较小值"""
        original_height = region['bbox'][3]
        
        # 统一为偶数较小值
        normalized = original_height - (original_height % 2)
        
        return normalized
    
    def get_layout_zone(self, region, layout_result):
        """基于布局分析确定文字所在的逻辑区域"""
        if not layout_result:
            return "unknown"
        
        x, y, w, h = region['bbox']
        center_y = y + h // 2
        
        # 获取所有区域的垂直分布信息
        all_regions = layout_result.get('regions', [])
        if not all_regions:
            return "body"
        
        # 计算垂直位置的分位数
        all_y_positions = [r['center'][1] for r in all_regions]
        all_y_positions.sort()
        
        total_regions = len(all_y_positions)
        if total_regions <= 1:
            return "body"
        
        # 分区逻辑：上1/3为header，中1/3为body，下1/3为footer
        top_threshold = all_y_positions[total_regions // 3]
        bottom_threshold = all_y_positions[2 * total_regions // 3]
        
        if center_y <= top_threshold:
            return "header"
        elif center_y >= bottom_threshold:
            return "footer"
        else:
            return "body"
    
    def apply_unified_constraints_to_group(self, group_regions, group_key):
        """为同一组的文字应用统一约束"""
        print(f"\n处理组 {group_key}:")
        
        # 第一步：计算每个区域的初始字体大小和尺寸
        for region in group_regions:
            self.calculate_initial_font_metrics(region)
        
        # 第二步：检查边框约束，找出最严格的缩放需求
        max_scale_factor = 1.0
        overflow_tolerance = 0.10  # 10%容忍度
        
        for region in group_regions:
            original_width = region['bbox'][2]
            translated_width = region['text_width']
            max_allowed_width = original_width * (1 + overflow_tolerance)
            
            if translated_width > max_allowed_width:
                required_scale = max_allowed_width / translated_width
                max_scale_factor = min(max_scale_factor, required_scale)
                print(f"  '{region['original_text']}': 宽度{translated_width}px > 限制{max_allowed_width:.1f}px, 需要缩放{required_scale:.3f}")
        
        # 第三步：应用统一的缩放因子
        if max_scale_factor < 1.0:
            print(f"  应用统一缩放因子: {max_scale_factor:.3f}")
            for region in group_regions:
                region['font_size'] = int(region['font_size'] * max_scale_factor)
                region['text_width'] = int(region['text_width'] * max_scale_factor)
                region['text_height'] = int(region['text_height'] * max_scale_factor)
                print(f"    '{region['original_text']}': {region['original_font_size']}px → {region['font_size']}px")
        else:
            print(f"  无需缩放，所有文字都在容忍范围内")
        
        # 记录分组信息
        for region in group_regions:
            region['group_key'] = group_key
            region['group_scale_factor'] = max_scale_factor
    
    def calculate_initial_font_metrics(self, region):
        """计算区域的初始字体度量"""
        x, y, w, h = region['bbox']
        style_info = region['style_info']
        font_path = self.font_mapping.get(region['font_info']['font'])
        
        # 使用高度匹配算法计算字体大小
        base_font_size = max(style_info['estimated_font_size'], 16)
        font_size = self.calculate_font_size_by_height_matching(
            region['translated_text'], h, font_path, base_font_size
        )
        
        # 计算文字尺寸
        text_width, text_height = self.get_text_dimensions(
            region['translated_text'], font_path, font_size
        )
        
        # 存储初始计算结果
        region['original_font_size'] = font_size
        region['font_size'] = font_size
        region['text_width'] = text_width
        region['text_height'] = text_height
    
    def calculate_layout_aware_layout(self, region, original_image, layout_result):
        """根据布局分析结果计算最终位置（字体大小已在分组约束中确定）"""
        x, y, w, h = region['bbox']
        
        # 使用已经计算好的字体大小和尺寸（来自分组约束）
        text_width = region['text_width']
        text_height = region['text_height']
        
        # 根据布局分析结果确定对齐方式
        alignment_type = self.determine_region_alignment(region, layout_result)
        
        # 根据对齐方式计算位置
        text_x, text_y = self.calculate_text_position(
            x, y, w, h, text_width, text_height, alignment_type
        )
        
        # 确保位置合理
        text_x = max(0, text_x)
        text_y = max(0, text_y)
        
        region.update({
            'text_x': text_x,
            'text_y': text_y,
            'alignment_type': alignment_type
        })
        
        # 显示分组和约束信息
        group_info = f"组{region.get('group_key', 'unknown')}"
        scale_info = f"缩放{region.get('group_scale_factor', 1.0):.3f}"
        print(f"    布局感知: '{region['original_text']}' → {group_info}, {scale_info}, 对齐{alignment_type}, 位置({text_x}, {text_y})")
    
    def determine_region_alignment(self, region, layout_result):
        """确定文字区域的对齐方式"""
        if not layout_result:
            return 'center'  # 默认居中对齐
        
        x, y, w, h = region['bbox']
        center_x = x + w // 2
        center_y = y + h // 2
        
        # 查找该区域属于哪个对齐组
        h_align = layout_result.get('horizontal_alignment', {})
        
        # 检查左对齐组
        left_groups = h_align.get('left_groups', [])
        for group in left_groups:
            for group_region in group:
                if (abs(group_region['left'] - x) <= 5 and 
                    abs(group_region['top'] - y) <= 5):
                    return 'left'
        
        # 检查居中对齐组  
        center_groups = h_align.get('center_groups', [])
        for group in center_groups:
            for group_region in group:
                group_center_x = group_region['center'][0]
                if abs(group_center_x - center_x) <= 5:
                    return 'center'
        
        # 检查右对齐组
        right_groups = h_align.get('right_groups', [])
        for group in right_groups:
            for group_region in group:
                if (abs(group_region['right'] - (x + w)) <= 5 and
                    abs(group_region['top'] - y) <= 5):
                    return 'right'
        
        # 新增：检查水平分布情况
        # 如果当前区域与其他区域在同一水平线上形成左右分布，则根据位置确定对齐方式
        all_regions = layout_result.get('regions', [])
        current_region = None
        
        # 找到当前区域对应的layout region
        for lr in all_regions:
            if (abs(lr['bbox'][0] - x) <= 5 and abs(lr['bbox'][1] - y) <= 5):
                current_region = lr
                break
        
        if current_region:
            # 查找在同一水平线上的其他区域
            horizontal_peers = []
            for lr in all_regions:
                if lr != current_region and abs(lr['center'][1] - current_region['center'][1]) <= 20:
                    horizontal_peers.append(lr)
            
            if horizontal_peers:
                # 如果有水平同伴，根据相对位置确定对齐方式
                image_center_x = 400  # 假设图片中心，可以从layout_result中获取更精确的值
                
                # 计算当前区域相对于图片中心的位置
                relative_pos = center_x - image_center_x
                
                # 如果在左半部分，使用左对齐；如果在右半部分，使用右对齐
                if relative_pos < -50:  # 明显在左侧
                    return 'left'
                elif relative_pos > 50:   # 明显在右侧  
                    return 'right'
                else:
                    return 'center'  # 在中间区域
        
        # 默认使用布局的主要对齐方式
        main_alignment = h_align.get('type', 'center')
        if main_alignment == 'mixed':
            return 'center'  # 混合情况默认居中
        
        return main_alignment
    
    def calculate_text_position(self, x, y, w, h, text_width, text_height, alignment_type):
        """根据对齐方式计算文字位置"""
        # 垂直位置始终居中
        text_y = y + (h - text_height) // 2
        
        # 根据对齐方式计算水平位置
        if alignment_type == 'left':
            text_x = x  # 左对齐
        elif alignment_type == 'right':
            text_x = x + w - text_width  # 右对齐
        else:  # 'center' 或其他情况
            text_x = x + (w - text_width) // 2  # 居中对齐
        
        return text_x, text_y

    def calculate_initial_layout_old(self, region):
        """计算区域的初始布局参数，使用智能空间利用策略"""
        x, y, w, h = region['bbox']
        style_info = region['style_info']
        font_path = self.font_mapping.get(region['font_info']['font'])
        
        # 计算初始字体大小
        base_font_size = max(style_info['estimated_font_size'], 16)
        font_size = self.calculate_font_size_with_style(
            region['translated_text'], w, h, font_path, base_font_size
        )
        
        # 智能字体大小优化：尝试保持合理的字体大小
        original_font_size = font_size
        
        print(f"    开始智能布局 '{region['original_text']}': 初始字体{font_size}, 区域{w}×{h}")
        
        # 第一阶段：尝试在严格边界内的最大字体
        max_attempts = 10
        attempts = 0
        
        while attempts < max_attempts:
            text_width, text_height = self.get_text_dimensions(
                region['translated_text'], font_path, font_size
            )
            
            print(f"      尝试字体{font_size}: 文字尺寸{text_width}×{text_height}, 严格限制{w}×{h}")
            
            # 严格边界检查
            if text_width <= w and text_height <= h:
                print(f"      ✓ 字体{font_size}适合严格边界")
                break
            
            font_size = max(12, int(font_size * 0.9))
            attempts += 1
            
            if font_size <= 12:
                print(f"      ! 达到最小字体限制12")
                break
        
        # 第二阶段：如果字体过小（缩小超过50%），尝试适度扩展区域
        reduction_percent = (original_font_size - font_size) / original_font_size
        
        if reduction_percent > 0.5:  # 如果缩小超过50%
            print(f"      字体缩小过多({reduction_percent*100:.0f}%)，尝试适度扩展布局...")
            
            # 尝试使用较大的字体并允许适度超出原始区域
            expanded_font_size = min(original_font_size, int(font_size * 1.5))
            
            for test_size in range(expanded_font_size, font_size, -2):
                test_width, test_height = self.get_text_dimensions(
                    region['translated_text'], font_path, test_size
                )
                
                # 允许宽度适度扩展（最多原区域的150%），但高度保持严格
                max_width = int(w * 1.5)
                
                if test_width <= max_width and test_height <= h:
                    print(f"      ✓ 扩展布局成功: 字体{test_size}, 尺寸{test_width}×{test_height}")
                    font_size = test_size
                    break
        
        # 重新计算最终的文字尺寸
        text_width, text_height = self.get_text_dimensions(
            region['translated_text'], font_path, font_size
        )
        
        # 智能位置计算：如果文字超出原始区域，尝试优化位置
        if text_width > w:
            # 文字超出宽度，尝试左对齐或右对齐来最大化利用空间
            text_x = x  # 左对齐
            print(f"      文字超宽，使用左对齐位置")
        else:
            # 正常居中
            text_x = x + (w - text_width) // 2
        
        text_y = y + (h - text_height) // 2
        
        # 确保位置合理
        text_x = max(0, text_x)
        text_y = max(0, text_y)
        
        region.update({
            'font_size': font_size,
            'text_width': text_width,
            'text_height': text_height,
            'text_x': text_x,
            'text_y': text_y,
            'is_multiline': False,
            'adjustments': [],
            'is_expanded': text_width > w  # 标记是否使用了扩展布局
        })
        
        final_reduction = (original_font_size - font_size) / original_font_size * 100
        expansion_info = f" [扩展布局]" if text_width > w else ""
        print(f"    最终智能布局: 字体{original_font_size}→{font_size} (缩小{final_reduction:.0f}%), 尺寸{text_width}×{text_height}{expansion_info}")
        print(f"    初始布局 '{region['original_text']}': 字体{font_size}, 尺寸({text_width}, {text_height}), 位置({text_x}, {text_y})")
    
    def detect_overlaps(self, regions):
        """检测区域重叠，智能处理扩展布局"""
        conflicts = []
        
        for i in range(len(regions)):
            for j in range(i + 1, len(regions)):
                region1, region2 = regions[i], regions[j]
                
                # 获取文字边界框
                x1, y1, w1, h1 = region1['text_x'], region1['text_y'], region1['text_width'], region1['text_height']
                x2, y2, w2, h2 = region2['text_x'], region2['text_y'], region2['text_width'], region2['text_height']
                
                # 计算重叠区域
                overlap_x = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
                overlap_y = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
                overlap_area = overlap_x * overlap_y
                
                # 智能冲突判断
                if overlap_area > 0:
                    # 计算重叠严重程度
                    area1 = w1 * h1
                    area2 = w2 * h2
                    overlap_ratio1 = overlap_area / area1 if area1 > 0 else 0
                    overlap_ratio2 = overlap_area / area2 if area2 > 0 else 0
                    max_overlap_ratio = max(overlap_ratio1, overlap_ratio2)
                    
                    # 检查是否为扩展布局
                    is_expanded1 = region1.get('is_expanded', False)
                    is_expanded2 = region2.get('is_expanded', False)
                    
                    # 智能阈值：扩展布局允许更小的重叠
                    if is_expanded1 or is_expanded2:
                        # 如果其中一个是扩展布局，允许最多15%的重叠
                        threshold = 0.15
                    else:
                        # 普通布局只允许5%的重叠
                        threshold = 0.05
                    
                    if max_overlap_ratio > threshold:
                        conflict_type = "严重" if max_overlap_ratio > 0.3 else "轻微"
                        print(f"    检测到{conflict_type}冲突: '{region1['original_text']}' 与 '{region2['original_text']}' (重叠{max_overlap_ratio*100:.1f}%)")
                        
                        conflicts.append({
                            'region1': region1,
                            'region2': region2,
                            'overlap_area': overlap_area,
                            'overlap_ratio': max_overlap_ratio,
                            'conflict_type': conflict_type
                        })
                    else:
                        print(f"    可接受重叠: '{region1['original_text']}' 与 '{region2['original_text']}' (重叠{max_overlap_ratio*100:.1f}%)")
        
        return conflicts
    
    def rectangles_overlap(self, rect1, rect2):
        """检查两个矩形是否重叠"""
        x1, y1, w1, h1 = rect1
        x2, y2, w2, h2 = rect2
        
        return not (x1 + w1 <= x2 or x2 + w2 <= x1 or 
                   y1 + h1 <= y2 or y2 + h2 <= y1)
    
    def calculate_overlap_area(self, region1, region2):
        """计算两个区域的重叠面积"""
        x1, y1, w1, h1 = region1['text_x'], region1['text_y'], region1['text_width'], region1['text_height']
        x2, y2, w2, h2 = region2['text_x'], region2['text_y'], region2['text_width'], region2['text_height']
        
        # 计算重叠矩形
        left = max(x1, x2)
        top = max(y1, y2)
        right = min(x1 + w1, x2 + w2)
        bottom = min(y1 + h1, y2 + h2)
        
        if left < right and top < bottom:
            return (right - left) * (bottom - top)
        return 0
    
    def resolve_conflicts(self, conflicts, regions, iteration):
        """解决重叠冲突，使用多级降级策略"""
        # 按重叠面积排序，优先解决重叠最严重的冲突
        conflicts.sort(key=lambda x: x['overlap_area'], reverse=True)
        
        for conflict in conflicts:
            region1 = conflict['region1']
            region2 = conflict['region2']
            
            print(f"  处理冲突: '{region1['original_text']}' 与 '{region2['original_text']}'")
            
            # 多级降级策略 - 每次迭代尝试所有策略
            strategies = [
                (self.try_position_adjustment, "位置调整"),
                (self.try_font_reduction, "字体缩小"),
                (self.try_multiline_layout, "分行显示")
            ]
            
            # 根据迭代次数调整策略优先级
            if iteration == 0:
                # 第一次迭代：优先位置调整
                pass
            elif iteration == 1:
                # 第二次迭代：优先字体缩小
                strategies = [
                    (self.try_font_reduction, "字体缩小"),
                    (self.try_position_adjustment, "位置调整"),
                    (self.try_multiline_layout, "分行显示")
                ]
            else:
                # 后续迭代：优先分行显示
                strategies = [
                    (self.try_multiline_layout, "分行显示"),
                    (self.try_font_reduction, "字体缩小"),
                    (self.try_position_adjustment, "位置调整")
                ]
            
            # 尝试所有策略
            resolved = False
            for strategy_func, strategy_name in strategies:
                if strategy_func == self.try_position_adjustment:
                    if strategy_func(region1, region2, regions):
                        print(f"    → 通过{strategy_name}解决冲突")
                        resolved = True
                        break
                else:
                    if strategy_func(region1, region2):
                        print(f"    → 通过{strategy_name}解决冲突")
                        resolved = True
                        break
            
            if not resolved:
                print(f"    → 无法解决冲突，将保持当前状态")
    
    def try_position_adjustment(self, region1, region2, all_regions):
        """尝试通过调整位置解决冲突"""
        print(f"      尝试位置调整...")
        print(f"        区域1 '{region1['original_text']}': 当前位置({region1['text_x']}, {region1['text_y']}), 尺寸({region1['text_width']}, {region1['text_height']})")
        print(f"        区域1 边界框: {region1['bbox']}")
        print(f"        区域2 '{region2['original_text']}': 当前位置({region2['text_x']}, {region2['text_y']}), 尺寸({region2['text_width']}, {region2['text_height']})")
        print(f"        区域2 边界框: {region2['bbox']}")
        
        # 为两个区域尝试不同的位置调整
        for region in [region1, region2]:
            original_x, original_y = region['text_x'], region['text_y']
            bbox_x, bbox_y, bbox_w, bbox_h = region['bbox']
            
            print(f"        尝试调整区域 '{region['original_text']}'...")
            
            # 在原始区域边界内尝试不同位置
            adjustments = [
                (10, 0),   # 右移
                (-10, 0),  # 左移
                (0, 10),   # 下移
                (0, -10),  # 上移
                (8, 8),    # 右下移
                (-8, -8),  # 左上移
                (15, 0),   # 大幅右移
                (-15, 0),  # 大幅左移
                (0, 15),   # 大幅下移
                (0, -15),  # 大幅上移
            ]
            
            for dx, dy in adjustments:
                new_x = original_x + dx
                new_y = original_y + dy
                
                # 检查是否仍在原始区域内
                if (new_x >= bbox_x and new_x + region['text_width'] <= bbox_x + bbox_w and
                    new_y >= bbox_y and new_y + region['text_height'] <= bbox_y + bbox_h):
                    
                    # 临时更新位置
                    region['text_x'] = new_x
                    region['text_y'] = new_y
                    
                    # 检查是否还有冲突
                    if not self.has_conflicts_with_others(region, all_regions):
                        region['adjustments'].append(f"位置调整({dx:+d},{dy:+d})")
                        print(f"          成功: 移动({dx:+d},{dy:+d}) -> 新位置({new_x}, {new_y})")
                        return True
                    else:
                        print(f"          尝试移动({dx:+d},{dy:+d}) -> ({new_x}, {new_y}): 仍有其他冲突")
                else:
                    print(f"          尝试移动({dx:+d},{dy:+d}) -> ({new_x}, {new_y}): 超出边界框")
            
            # 恢复原始位置
            region['text_x'] = original_x
            region['text_y'] = original_y
        
        print(f"        位置调整失败")
        return False
    
    def try_font_reduction(self, region1, region2):
        """尝试通过减小字体解决冲突"""
        print(f"      尝试字体缩小...")
        
        for region in [region1, region2]:
            original_size = region['font_size']
            min_size = int(original_size * 0.7)  # 最多缩小到70%
            
            print(f"        尝试缩小区域 '{region['original_text']}' 字体: {original_size} -> {min_size}")
            
            for new_size in range(original_size - 2, min_size - 1, -2):
                if new_size < 12:  # 最小字体限制
                    print(f"          字体 {new_size} 太小，停止尝试")
                    break
                
                # 重新计算文字尺寸
                font_path = self.font_mapping.get(region['font_info']['font'])
                new_width, new_height = self.get_text_dimensions(
                    region['translated_text'], font_path, new_size
                )
                
                # 重新计算居中位置
                bbox_x, bbox_y, bbox_w, bbox_h = region['bbox']
                new_x = bbox_x + (bbox_w - new_width) // 2
                new_y = bbox_y + (bbox_h - new_height) // 2
                
                print(f"          尝试字体 {new_size}: 尺寸({new_width}, {new_height}), 位置({new_x}, {new_y})")
                
                # 临时更新参数
                old_values = (region['font_size'], region['text_width'], 
                             region['text_height'], region['text_x'], region['text_y'])
                
                region.update({
                    'font_size': new_size,
                    'text_width': new_width,
                    'text_height': new_height,
                    'text_x': new_x,
                    'text_y': new_y
                })
                
                # 检查是否解决了冲突
                if not self.rectangles_overlap(
                    (region1['text_x'], region1['text_y'], region1['text_width'], region1['text_height']),
                    (region2['text_x'], region2['text_y'], region2['text_width'], region2['text_height'])
                ):
                    reduction_percent = int((1 - new_size/original_size) * 100)
                    region['adjustments'].append(f"字体缩小{reduction_percent}%")
                    print(f"          成功: 字体缩小到 {new_size}")
                    return True
                else:
                    print(f"          字体 {new_size}: 仍有重叠")
                
                # 恢复原始值
                region['font_size'], region['text_width'], region['text_height'], region['text_x'], region['text_y'] = old_values
        
        print(f"        字体缩小失败")
        return False
    
    def try_multiline_layout(self, region1, region2):
        """尝试通过分行显示解决冲突"""
        for region in [region1, region2]:
            text = region['translated_text']
            if len(text) < 4:  # 太短的文字不适合分行
                continue
            
            # 尝试分成两行
            mid_point = len(text) // 2
            line1 = text[:mid_point]
            line2 = text[mid_point:]
            
            # 计算分行后的尺寸
            font_path = self.font_mapping.get(region['font_info']['font'])
            font_size = region['font_size']
            
            line1_w, line1_h = self.get_text_dimensions(line1, font_path, font_size)
            line2_w, line2_h = self.get_text_dimensions(line2, font_path, font_size)
            
            total_width = max(line1_w, line2_w)
            total_height = line1_h + line2_h + 2  # 加上行间距
            
            bbox_x, bbox_y, bbox_w, bbox_h = region['bbox']
            
            # 检查是否能放入原始区域
            if total_width <= bbox_w and total_height <= bbox_h:
                # 更新为分行布局
                region.update({
                    'is_multiline': True,
                    'line1': line1,
                    'line2': line2,
                    'text_width': total_width,
                    'text_height': total_height,
                    'text_x': bbox_x + (bbox_w - total_width) // 2,
                    'text_y': bbox_y + (bbox_h - total_height) // 2
                })
                
                # 检查是否解决了冲突
                if not self.rectangles_overlap(
                    (region1['text_x'], region1['text_y'], region1['text_width'], region1['text_height']),
                    (region2['text_x'], region2['text_y'], region2['text_width'], region2['text_height'])
                ):
                    region['adjustments'].append("分行显示")
                    return True
        
        return False
    
    def has_conflicts_with_others(self, target_region, all_regions):
        """检查目标区域是否与其他区域有冲突"""
        for region in all_regions:
            if region['id'] == target_region['id']:
                continue
            
            if self.rectangles_overlap(
                (target_region['text_x'], target_region['text_y'], 
                 target_region['text_width'], target_region['text_height']),
                (region['text_x'], region['text_y'], 
                 region['text_width'], region['text_height'])
            ):
                return True
        return False
    
    def get_text_dimensions(self, text, font_path, font_size):
        """获取文字的精确尺寸"""
        try:
            if font_path and os.path.exists(font_path):
                if self.is_variable_font(font_path):
                    font = self.create_variable_font(font_path, font_size)
                else:
                    font = ImageFont.truetype(font_path, font_size)
            else:
                font = ImageFont.load_default()
            
            bbox = font.getbbox(text)
            width = bbox[2] - bbox[0]
            height = bbox[3] - bbox[1]
            return width, height
        except:
            # 如果出错，使用估算值
            return len(text) * font_size * 0.6, font_size
    
    def draw_optimized_text(self, image, region, original_image):
        """使用高度匹配绘制文字"""
        # 转换为PIL图像进行文字绘制
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_image)
        
        # 准备字体和颜色
        font_path = self.font_mapping.get(region['font_info']['font'])
        try:
            if font_path:
                # 检查是否为可变字体
                if self.is_variable_font(font_path):
                    font = self.create_variable_font(font_path, region['font_size'])
                else:
                    font = ImageFont.truetype(font_path, region['font_size'])
            else:
                font = ImageFont.load_default()
        except:
            font = ImageFont.load_default()
        
        style_info = region['style_info']
        if len(style_info['text_color']) == 3:
            text_color_rgb = (style_info['text_color'][2], style_info['text_color'][1], style_info['text_color'][0])
        else:
            text_color_rgb = style_info['text_color']
        
        # 绘制文字
        if style_info['is_bold']:
            self.draw_bold_text(draw, region['translated_text'], region['text_x'], region['text_y'], font, text_color_rgb)
        else:
            draw.text((region['text_x'], region['text_y']), region['translated_text'], font=font, fill=text_color_rgb)
        

        
        # 转换回OpenCV格式
        return cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)

    def is_variable_font(self, font_path):
        """
        检测字体文件是否为可变字体
        
        参数:
        font_path: 字体文件路径
        
        返回:
        bool: 是否为可变字体
        """
        if not font_path or not os.path.exists(font_path):
            return False
            
        try:
            # 加载字体并检查是否有可变轴
            font = ImageFont.truetype(font_path, 16)
            axes = font.get_variation_axes()
            return len(axes) > 0
        except (OSError, AttributeError):
            return False

    def create_variable_font(self, font_path, size, weight=None):
        """
        创建可变字体对象，支持粗细调节
        
        参数:
        font_path: 字体文件路径
        size: 字体大小
        weight: 粗细值 (100-900)，如果为None则使用self.weight_adjustment
        
        返回:
        PIL ImageFont对象
        """
        if weight is None:
            weight = self.weight_adjustment
            
        try:
            # 加载字体
            font = ImageFont.truetype(font_path, size)
            
            # 检查是否为可变字体
            try:
                # 获取可变字体的轴信息
                axes = font.get_variation_axes()
                
                # 查找weight轴
                weight_axis = None
                for axis in axes:
                    axis_name = axis.get('name', b'')
                    if isinstance(axis_name, bytes):
                        axis_name_str = axis_name.decode('utf-8', errors='ignore').lower()
                    else:
                        axis_name_str = str(axis_name).lower()
                    
                    if (axis_name == b'wght' or 
                        axis_name == b'Weight' or 
                        'weight' in axis_name_str or
                        'wght' in axis_name_str):
                        weight_axis = axis
                        break
                
                if weight_axis:
                    # 限制weight值在字体支持的范围内
                    min_weight = weight_axis.get('minimum', 100)
                    max_weight = weight_axis.get('maximum', 900)
                    adjusted_weight = max(min_weight, min(max_weight, weight))
                    
                    print(f"    设置可变字体粗细: {weight} -> {adjusted_weight} (范围: {min_weight}-{max_weight})")
                    
                    # 设置字体粗细 - 使用正确的方法
                    try:
                        # 使用轴索引列表方式设置
                        weight_axis_index = None
                        for idx, axis in enumerate(axes):
                            axis_name_check = axis.get('name', b'')
                            if isinstance(axis_name_check, bytes):
                                axis_name_str = axis_name_check.decode('utf-8', errors='ignore').lower()
                            else:
                                axis_name_str = str(axis_name_check).lower()
                            
                            if (axis_name_check == b'wght' or 
                                axis_name_check == b'Weight' or 
                                'weight' in axis_name_str or
                                'wght' in axis_name_str):
                                weight_axis_index = idx
                                break
                        
                        if weight_axis_index is not None:
                            # 创建轴值列表，只修改weight轴
                            axis_values = [axis.get('default', 400) for axis in axes]
                            axis_values[weight_axis_index] = adjusted_weight
                            font.set_variation_by_axes(axis_values)
                        else:
                            print(f"      找不到weight轴索引")
                    except Exception as e:
                        print(f"      设置粗细失败: {e}")
                else:
                    print(f"    字体不支持weight轴，使用默认粗细")
                    
            except (OSError, AttributeError) as e:
                print(f"    非可变字体或不支持粗细调节: {e}")
                
            return font
            
        except Exception as e:
            print(f"    字体加载失败: {e}")
            return ImageFont.load_default()

    def measure_roi_text_height(self, roi_bgr, prefix=None):
        """对OCR裁剪区域测真实笔画高度，并在test_output中保存debug图像
        参数:
            roi_bgr: 裁剪的BGR图像
            prefix: 保存文件名前缀（可选）
        返回:
            文字实际高度 (像素)
        """
        import os, cv2, numpy as np
        from PIL import Image
        os.makedirs("test_output", exist_ok=True)

        gray = cv2.cvtColor(roi_bgr, cv2.COLOR_BGR2GRAY)
        # 1) 轻度高斯模糊，降低噪点影响
        blur = cv2.GaussianBlur(gray, (3, 3), 0)
        # 2) Otsu 自适应阈值，自动找最佳分割点
        _, binary = cv2.threshold(blur, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # 3) 闭运算 (膨胀→腐蚀) 填补细小空洞，但不削弱笔画尖端
        kernel = np.ones((3, 3), np.uint8)
        binary_clean = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel, iterations=1)

        contours, _ = cv2.findContours(binary_clean, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            # 没有找到文字像素，返回ROI高度
            return roi_bgr.shape[0]

        # 合并所有轮廓点，得到整体文字外框
        all_pts = np.vstack(contours)
        x, y, w, h = cv2.boundingRect(all_pts)

        # 调试图像
        if prefix:
            safe_prefix = prefix.replace("/", "_").replace(" ", "_")
            # 原图
            cv2.imwrite(os.path.join("test_output", f"{safe_prefix}_roi.png"), roi_bgr)
            # Binary
            cv2.imwrite(os.path.join("test_output", f"{safe_prefix}_binary.png"), binary_clean)
            # 标注bbox
            bbox_img = roi_bgr.copy()
            cv2.rectangle(bbox_img, (x, y), (x + w, y + h), (0, 0, 255), 2)
            cv2.imwrite(os.path.join("test_output", f"{safe_prefix}_bbox.png"), bbox_img)

        return h

# 使用示例
if __name__ == "__main__":
    # 测试翻译器
    translator = SimpleTranslator()
    
    # 测试翻译功能
    test_texts = ["护脊靠枕", "呵护爱宠脊椎", "未知文字"]
    
    for text in test_texts:
        translated = translator.translate_text(text)
        print(f"'{text}' → '{translated}'") 
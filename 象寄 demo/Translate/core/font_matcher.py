from PIL import Image, ImageDraw, ImageFont
import cv2
import numpy as np
import os
from skimage.metrics import structural_similarity as ssim
from fontTools.ttLib import TTFont  # 新增，用于读取字体支持字集
import json

class SimpleFontMatcher:
    def __init__(self):
        """初始化字体匹配器，只使用本地项目中的字体"""
        # 定义本地字体配置
        self.fonts = {}
        
        # 获取项目根目录的fonts文件夹路径
        base_dir = os.path.dirname(os.path.dirname(__file__))  # 从core目录向上一级到项目根目录
        fonts_dir = os.path.join(base_dir, 'fonts')
        
        # 检查fonts目录是否存在
        if not os.path.exists(fonts_dir):
            print(f"错误：fonts目录不存在于 {fonts_dir}")
            return
            
        # 扫描本地字体目录，添加所有可用字体
        font_configs = {
            'NotoSansSC': {
                'path': 'NotoSansSC/NotoSansSC-Black.ttf',
                'name': 'Noto Sans SC Black'
            },
            'SpoqaHanSans': {
                'path': 'SpoqaHanSans/SpoqaHanSansBold.ttf', 
                'name': 'Spoqa Han Sans Bold'
            },
            '台北黑体': {
                'path': '台北黑体/TaipeiSans-Bold.ttf',
                'name': '台北黑体 TC Bold'
            },
            '思源黑体': {
                'path': '思源黑体/SourceHanSans-VF.otf',
                'name': '思源黑体'
            }
        }
        
        # 加载本地字体
        for font_key, config in font_configs.items():
            font_path = os.path.join(fonts_dir, config['path'])
            if os.path.exists(font_path):
                self.fonts[config['name']] = font_path
                print(f"已加载本地字体: {config['name']} -> {font_path}")
            else:
                print(f"警告：字体文件不存在 {font_path}")
        
        if not self.fonts:
            print("错误：没有找到任何可用的本地字体")
            return
            
        print(f"总共加载了 {len(self.fonts)} 个本地字体: {list(self.fonts.keys())}")
        
        # ---- 字形集合缓存与懒加载 ----
        self._glyph_cache_path = os.path.join(os.path.dirname(__file__), 'font_glyphs_cache.json')

        # 加载已有缓存
        self._glyph_cache = {}
        if os.path.exists(self._glyph_cache_path):
            try:
                with open(self._glyph_cache_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                # 转成 set 方便后续判断
                self._glyph_cache = {k: set(v) for k, v in data.items()}
            except Exception as e:
                print(f"字体字形缓存加载失败，已跳过: {e}")

        # 初始化 font_glyphs，不立即解析未缓存字体
        self.font_glyphs = {}
        for name in self.fonts.keys():
            if name in self._glyph_cache:
                self.font_glyphs[name] = self._glyph_cache[name]
            else:
                self.font_glyphs[name] = None  # 标记为未加载

    
    def generate_font_sample(self, text, font_path, size=32):
        """生成指定字体的文字样本"""
        try:
            # 尝试加载字体
            try:
                font = ImageFont.truetype(font_path, size)
            except:
                # 如果加载失败，使用默认字体
                font = ImageFont.load_default()
            
            # 计算文字的实际边界框
            bbox = font.getbbox(text)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # 计算字体的基线偏移
            ascent, descent = font.getmetrics()
            
            # 设置足够的边距，确保文字不被截断
            padding_x = 15
            padding_y = max(15, descent + 5)  # 根据下降部分调整垂直边距
            
            canvas_width = text_width + 2 * padding_x
            canvas_height = ascent + descent + 2 * padding_y
            
            # 创建白色背景图像
            img = Image.new('RGB', (canvas_width, canvas_height), 'white')
            draw = ImageDraw.Draw(img)
            
            # 计算文字绘制位置，确保完整显示
            draw_x = padding_x - bbox[0]  # 补偿左边界偏移
            draw_y = padding_y - bbox[1]  # 补偿上边界偏移
            
            # 绘制黑色文字
            draw.text((draw_x, draw_y), text, font=font, fill='black')
            
            return np.array(img)
            
        except Exception as e:
            print(f"生成字体样本失败 {font_path}: {e}")
            return None
    
    def preprocess_image(self, image):
        """预处理图像，统一格式"""
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image.copy()
        
        # 二值化
        _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        
        # 去除边框噪声
        kernel = np.ones((2,2), np.uint8)
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return binary
    
    def resize_to_match(self, img1, img2):
        """调整两个图像到相同尺寸"""
        h1, w1 = img1.shape[:2]
        h2, w2 = img2.shape[:2]
        
        # 选择较大的尺寸
        target_h = max(h1, h2)
        target_w = max(w1, w2)
        
        # 调整尺寸
        img1_resized = cv2.resize(img1, (target_w, target_h))
        img2_resized = cv2.resize(img2, (target_w, target_h))
        
        return img1_resized, img2_resized
    
    def calculate_similarity(self, img1, img2):
        """计算两个图像的相似度"""
        # 预处理
        proc1 = self.preprocess_image(img1)
        proc2 = self.preprocess_image(img2)
        
        # 调整尺寸
        proc1, proc2 = self.resize_to_match(proc1, proc2)
        
        # 计算结构相似性
        try:
            similarity = ssim(proc1, proc2)
        except:
            # 如果SSIM失败，使用模板匹配
            result = cv2.matchTemplate(proc1, proc2, cv2.TM_CCOEFF_NORMED)
            similarity = np.max(result)
        
        return similarity
    
    def _ensure_glyphs(self, font_name, font_path):
        """若尚未解析该字体的字形集合，则解析并缓存"""
        if self.font_glyphs.get(font_name) is not None:
            return  # 已有集合

        char_codes = set()
        try:
            tt = TTFont(font_path, lazy=True)
            for table in tt['cmap'].tables:
                char_codes.update(table.cmap.keys())
        except Exception:
            # 解析失败则保持空集
            pass

        self.font_glyphs[font_name] = char_codes

        # 写入缓存文件
        try:
            # 更新内存 cache
            self._glyph_cache[font_name] = char_codes
            serializable = {k: sorted(list(v)) for k, v in self._glyph_cache.items()}
            with open(self._glyph_cache_path, 'w', encoding='utf-8') as f:
                json.dump(serializable, f, ensure_ascii=False)
        except Exception as e:
            print(f"写入字体字形缓存失败: {e}")
    
    def match_font(self, text_image, recognized_text):
        """匹配最相似的字体"""
        if not self.fonts:
            return "无可用字体", 0.0
        
        best_font = None
        best_score = -1
        results = {}
        
        # 单字级匹配 + 投票
        vote_counter = {}
        score_accumulator = {}

        sample_text = recognized_text  # 使用完整文本进行单字投票

        for ch in sample_text:
            ch_code = ord(ch)
            # 跳过非 CJK 字符
            if not (0x4e00 <= ch_code <= 0x9fff):
                continue

            for font_name, font_path in self.fonts.items():
                # 确保已加载字形集合
                self._ensure_glyphs(font_name, font_path)

                # 缺字过滤：如果字体不含该字符，则跳过
                if ch_code not in self.font_glyphs.get(font_name, set()):
                    continue

                sample_image = self.generate_font_sample(ch, font_path, size=32)
                if sample_image is None:
                    continue

                similarity = self.calculate_similarity(text_image, sample_image)

                # 将结果累积
                score_accumulator.setdefault(font_name, 0.0)
                vote_counter.setdefault(font_name, 0)
                score_accumulator[font_name] += similarity
                vote_counter[font_name] += 1

        # 计算平均分并确定最佳字体
        all_scores = []
        for font_name in vote_counter:
            avg_score = score_accumulator[font_name] / vote_counter[font_name]
            results[font_name] = avg_score
            all_scores.append((avg_score, font_name))
        
        # 多样化字体选择策略
        if all_scores:
            all_scores.sort(reverse=True, key=lambda x: x[0])
            
            # 更宽松的相似度阈值，找出差距小于0.15的字体
            best_score = all_scores[0][0]
            similar_fonts = [item for item in all_scores if best_score - item[0] < 0.15]
            
            # 强制多样性：优先选择非思源黑体的字体
            non_siyuan_fonts = [item for item in similar_fonts if '思源黑体' not in item[1]]
            
            if non_siyuan_fonts:
                # 如果有非思源黑体的相似字体，优先从中选择
                import hashlib
                text_hash = int(hashlib.md5(recognized_text.encode()).hexdigest(), 16)
                selected_index = text_hash % len(non_siyuan_fonts)
                best_score, best_font = non_siyuan_fonts[selected_index]
            elif len(similar_fonts) > 1:
                # 如果只有思源黑体相似，还是用轮换机制
                import hashlib
                text_hash = int(hashlib.md5(recognized_text.encode()).hexdigest(), 16)
                selected_index = text_hash % len(similar_fonts)
                best_score, best_font = similar_fonts[selected_index]
            else:
                best_score, best_font = all_scores[0]
        
        return best_font, best_score
    
    def save_comparison(self, text_image, recognized_text, output_dir="output"):
        """保存字体比较结果图像"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        sample_text = recognized_text[:3] if len(recognized_text) > 3 else recognized_text
        
        # 创建比较图像
        comparison_images = []
        labels = []
        
        # 添加原始图像
        comparison_images.append(text_image)
        labels.append("Original")
        
        # 动态调整字体大小以获得更好的渲染效果
        base_font_size = max(32, min(48, len(sample_text) * 16))  # 根据字符数调整基础大小
        
        # 生成各种字体样本
        for font_name, font_path in self.fonts.items():
            sample = self.generate_font_sample(sample_text, font_path, size=base_font_size)
            if sample is not None:
                comparison_images.append(sample)
                labels.append(font_name)
        
        if comparison_images:
            # 设置统一的目标尺寸，避免过度缩放
            target_content_height = 60  # 固定内容高度
            label_area = 25  # 增加标签高度
            target_total_height = target_content_height + label_area
            annotated_images = []
            
            # 准备绘制标签的字体
            # 优先使用系统中文字体，其次使用项目字体，最后退回默认字体
            label_font = None
            try:
                import platform
                sys_name = platform.system()
                if sys_name == "Windows":
                    label_font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 12)
                elif sys_name == "Darwin":
                    label_font = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 12)
                else:
                    # 常见 Noto Sans CJK 路径
                    label_font = ImageFont.truetype("/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc", 12)
            except Exception:
                pass

            # 如果系统字体加载失败，尝试使用项目内字体
            if label_font is None:
                for _p in self.fonts.values():
                    try:
                        label_font = ImageFont.truetype(_p, 12)
                        break
                    except Exception:
                        continue

            # 最后退回默认字体
            if label_font is None:
                try:
                    label_font = ImageFont.load_default()
                except Exception:
                    label_font = None

            for img, label in zip(comparison_images, labels):
                # 灰度转RGB
                if len(img.shape) == 2:
                    img = cv2.cvtColor(img, cv2.COLOR_GRAY2RGB)
                
                # 计算合适的宽度以保持比例
                original_h, original_w = img.shape[:2]
                ratio = target_content_height / original_h
                target_width = max(100, int(original_w * ratio))  # 最小宽度100px
                
                # 使用高质量插值方法调整图像大小
                resized_content = cv2.resize(img, (target_width, target_content_height), 
                                           interpolation=cv2.INTER_LANCZOS4)
                
                # 创建带标签的空白画布
                canvas = np.full((target_total_height, target_width, 3), 255, dtype=np.uint8)
                
                # 居中放置内容，确保对齐
                canvas[:target_content_height, :, :] = resized_content
                
                # 使用Pillow绘制标签，支持中文
                canvas_pil = Image.fromarray(canvas)
                draw = ImageDraw.Draw(canvas_pil)
                
                # 计算标签位置，使其居中
                bbox = draw.textbbox((0, 0), label, font=label_font)
                text_width = bbox[2] - bbox[0]
                text_x = max(2, (target_width - text_width) // 2)  # 居中，最小边距2px
                
                draw.text((text_x, target_content_height + 3), label, fill=(0, 0, 0), font=label_font)
                canvas = np.array(canvas_pil)
                annotated_images.append(canvas)
            
            # 水平拼接，每个图片之间添加分隔线
            if annotated_images:
                # 添加分隔线
                separator_width = 2
                separator = np.full((target_total_height, separator_width, 3), 200, dtype=np.uint8)
                
                final_images = [annotated_images[0]]  # 原始图像
                for img in annotated_images[1:]:
                    final_images.append(separator)
                    final_images.append(img)
                
                comparison = np.hstack(final_images)
            else:
                comparison = annotated_images[0]
            
            # 保存结果
            if not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, "font_comparison_first.png")
            cv2.imwrite(output_path, cv2.cvtColor(comparison, cv2.COLOR_RGB2BGR))
            print(f"字体比较图已保存: {output_path}")

    def save_debug_comparison(self, text_image, recognized_text, matched_font, similarity_score, output_dir="output", region_id=None):
        """保存字体匹配调试图像，显示原文和所有字体样本对比"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 使用识别的文字生成样本
        sample_text = recognized_text
        
        # 创建比较图像列表
        comparison_images = []
        labels = []
        similarities = []
        
        # 添加原始图像
        comparison_images.append(text_image)
        labels.append(f"原文\n'{recognized_text}'")
        similarities.append("")
        
        # 动态调整字体大小
        base_font_size = max(24, min(40, len(sample_text) * 12))
        
        # 为每个可用字体生成样本并计算相似度
        font_results = []
        for font_name, font_path in self.fonts.items():
            # 确保字体支持这些字符
            self._ensure_glyphs(font_name, font_path)
            
            # 检查字体是否支持所有字符
            supported_chars = []
            for ch in sample_text:
                ch_code = ord(ch)
                if 0x4e00 <= ch_code <= 0x9fff:  # 中文字符
                    if ch_code in self.font_glyphs.get(font_name, set()):
                        supported_chars.append(ch)
            
            if not supported_chars:
                continue  # 字体不支持任何字符，跳过
            
            # 生成字体样本
            sample = self.generate_font_sample(sample_text, font_path, size=base_font_size)
            if sample is not None:
                # 计算与原图的相似度
                similarity = self.calculate_similarity(text_image, sample)
                font_results.append((similarity, font_name, sample))
        
        # 按相似度排序
        font_results.sort(reverse=True, key=lambda x: x[0])
        
        # 添加所有字体样本到比较列表
        for similarity, font_name, sample in font_results:
            comparison_images.append(sample)
            # 标记匹配的字体
            if font_name == matched_font:
                labels.append(f"★ {font_name}\n(已选择)")
            else:
                labels.append(f"{font_name}")
            similarities.append(f"相似度: {similarity:.3f}")
        
        if len(comparison_images) <= 1:
            print(f"警告：没有足够的字体样本生成调试图像")
            return
        
        # 设置图像布局参数
        target_content_height = 50
        label_area = 35  # 增加标签区域高度
        similarity_area = 15  # 相似度信息区域
        target_total_height = target_content_height + label_area + similarity_area
        
        # 准备绘制标签的字体
        # 优先使用系统中文字体，其次使用项目字体，最后退回默认字体
        label_font = None
        try:
            import platform
            sys_name = platform.system()
            if sys_name == "Windows":
                label_font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 12)
            elif sys_name == "Darwin":
                label_font = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 12)
            else:
                # 常见 Noto Sans CJK 路径
                label_font = ImageFont.truetype("/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc", 12)
        except Exception:
            pass

        # 如果系统字体加载失败，尝试使用项目内字体
        if label_font is None:
            for _p in self.fonts.values():
                try:
                    label_font = ImageFont.truetype(_p, 12)
                    break
                except Exception:
                    continue

        # 最后退回默认字体
        if label_font is None:
            try:
                label_font = ImageFont.load_default()
            except Exception:
                label_font = None

        annotated_images = []
        
        for i, (img, label, sim_text) in enumerate(zip(comparison_images, labels, similarities)):
            # 转换图像格式
            if len(img.shape) == 2:
                img = cv2.cvtColor(img, cv2.COLOR_GRAY2RGB)
            
            # 计算合适的宽度以保持比例
            original_h, original_w = img.shape[:2]
            ratio = target_content_height / original_h
            target_width = max(80, int(original_w * ratio))
            
            # 调整图像大小
            resized_content = cv2.resize(img, (target_width, target_content_height), 
                                       interpolation=cv2.INTER_LANCZOS4)
            
            # 创建带标签的画布
            canvas = np.full((target_total_height, target_width, 3), 255, dtype=np.uint8)
            
            # 放置图像内容
            canvas[:target_content_height, :, :] = resized_content
            
            # 使用Pillow绘制标签
            if label_font:
                canvas_pil = Image.fromarray(canvas)
                draw = ImageDraw.Draw(canvas_pil)
                
                # 绘制字体名称标签
                lines = label.split('\n')
                y_offset = target_content_height + 2
                for line in lines:
                    if line.strip():
                        try:
                            bbox = draw.textbbox((0, 0), line, font=label_font)
                            text_width = bbox[2] - bbox[0]
                            text_x = max(2, (target_width - text_width) // 2)
                            
                            # 为选中的字体使用红色
                            color = (255, 0, 0) if line.startswith('★') else (0, 0, 0)
                            draw.text((text_x, y_offset), line, fill=color, font=label_font)
                            y_offset += 12
                        except:
                            # 如果绘制失败，使用简化版本
                            draw.text((2, y_offset), line[:10], fill=(0, 0, 0), font=label_font)
                            y_offset += 12
                
                # 绘制相似度信息
                if sim_text and i > 0:  # 跳过原图
                    try:
                        bbox = draw.textbbox((0, 0), sim_text, font=label_font)
                        text_width = bbox[2] - bbox[0]
                        text_x = max(2, (target_width - text_width) // 2)
                        draw.text((text_x, target_content_height + label_area), sim_text, 
                                fill=(0, 100, 0), font=label_font)
                    except:
                        pass
                
                canvas = np.array(canvas_pil)
            
            annotated_images.append(canvas)
        
        # 水平拼接所有图像
        if annotated_images:
            # 添加分隔线
            separator_width = 2
            separator = np.full((target_total_height, separator_width, 3), 128, dtype=np.uint8)
            
            final_images = [annotated_images[0]]  # 原始图像
            for img in annotated_images[1:]:
                final_images.append(separator)
                final_images.append(img)
            
            comparison = np.hstack(final_images)
            
            # 添加顶部标题栏
            title_height = 25
            title_canvas = np.full((title_height, comparison.shape[1], 3), 240, dtype=np.uint8)
            
            if label_font:
                title_pil = Image.fromarray(title_canvas)
                title_draw = ImageDraw.Draw(title_pil)
                
                title_text = f"字体匹配调试 - 文字: '{recognized_text}' | 选择: {matched_font} | 相似度: {similarity_score:.3f}"
                try:
                    title_draw.text((10, 5), title_text, fill=(0, 0, 0), font=label_font)
                except:
                    title_draw.text((10, 5), f"Font Debug - {recognized_text}", fill=(0, 0, 0), font=label_font)
                
                title_canvas = np.array(title_pil)
            
            # 合并标题和内容
            final_result = np.vstack([title_canvas, comparison])
        else:
            final_result = annotated_images[0]
        
        # 保存结果
        region_suffix = f"_region_{region_id}" if region_id is not None else ""
        filename = f"font_debug_{recognized_text[:5].replace(' ', '_')}{region_suffix}.png"
        # 清理文件名中的特殊字符
        import re
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        
        output_path = os.path.join(output_dir, filename)
        cv2.imwrite(output_path, cv2.cvtColor(final_result, cv2.COLOR_RGB2BGR))
        print(f"字体匹配调试图已保存: {output_path}")
        
        return output_path

# 使用示例
if __name__ == "__main__":
    # 测试字体匹配器
    matcher = SimpleFontMatcher()
    
    # 创建测试图像
    test_text = "测试文字"
    test_font_path = matcher.fonts.get('宋体') or matcher.fonts.get('微软雅黑')
    
    if test_font_path:
        test_image = matcher.generate_font_sample(test_text, test_font_path)
        if test_image is not None:
            best_font, score = matcher.match_font(test_image, test_text)
            print(f"\n最佳匹配: {best_font} (相似度: {score:.3f})")
            
            # 保存比较结果
            matcher.save_comparison(test_image, test_text) 
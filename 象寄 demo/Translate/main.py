"""
图像翻译程序主入口

使用示例:
  python main.py                                    # 默认处理 example.jpg
  python main.py --image test.jpg --font-weight 700  # 指定图像和字体
  python main.py --enable-ocr-debug                 # 启用OCR调试
  python main.py --enable-layout-debug              # 启用布局调试
  python main.py --no-debug --output-dir results    # 关闭调试，指定输出目录

字体粗细: 100(超细) 200(细) 300(较细) 400(正常) 500(中等) 600(半粗) 700(粗) 800(超粗) 900(黑体)
"""
import os
import sys
import argparse
from pipeline import TranslationPipeline


def main():
    """主函数"""
    # 参数解析
    parser = argparse.ArgumentParser(description='图像文字翻译工具')
    parser.add_argument('--image', '-i', default='example.jpg', help='输入图像文件路径')
    parser.add_argument('--font-weight', '-w', type=int, default=400, choices=range(100, 901, 100), help='字体粗细')
    parser.add_argument('--no-debug', action='store_true', help='禁用调试输出')
    parser.add_argument('--output-dir', '-o', default='output', help='输出目录')
    parser.add_argument('--enable-ocr-debug', action='store_true', help='启用OCR调试')
    parser.add_argument('--enable-layout-debug', action='store_true', help='启用布局调试')
    args = parser.parse_args()
    
    # 基本信息
    print(f"图片翻译Demo | 图片: {args.image} | 字体粗细: {args.font_weight}")
    
    # 验证输入
    if not os.path.exists(args.image):
        print(f"错误: 找不到图像文件 {args.image}")
        return 1
    
    # 处理流程
    pipeline = None
    try:
        # 创建流水线
        print('\n')
        print("🚀开始流水线")
        pipeline = TranslationPipeline(args.font_weight, not args.no_debug)
        pipeline.update_config(
            output_dir=args.output_dir,
            enable_ocr_debug=args.enable_ocr_debug,
            enable_layout_debug=args.enable_layout_debug
        )
        
        # 执行翻译
        result = pipeline.process_image(args.image)
        
        # 显示结果
        if result.success:
            data = result.data
            if 'render_log' in data:
                print(f"翻译完成，处理了 {len(data['render_log'])} 个文字")
                # 详细结果在注释中: 原文 → 译文 (字体, 大小)
            else:
                print(data.get('message', '处理完成'))
        else:
            print(f"处理失败: {result.error_message}")
            return 1
            
    except KeyboardInterrupt:
        print("\n用户中断")
        return 0
    except Exception as e:
        print(f"程序异常: {e}")
        return 1
    finally:
        if pipeline:
            pipeline.cleanup()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())

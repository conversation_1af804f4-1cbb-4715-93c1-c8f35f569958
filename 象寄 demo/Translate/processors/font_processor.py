"""
字体处理器
负责字体识别与匹配
"""
import cv2
import numpy as np
from typing import List, Tuple
from PIL import Image, ImageDraw, ImageFont
from skimage.metrics import structural_similarity as ssim
from fontTools.ttLib import TTFont

from models.data_models import TextRegion, FontMatchResult, ProcessingResult
from config.settings import get_config_manager


class FontProcessor:
    """字体处理器"""
    
    def __init__(self):
        """初始化字体处理器"""
        self.config_manager = get_config_manager()
        self.font_mapping = self.config_manager.font_mapping
        self.sample_size = (64, 64)
        self.font_size = 32
        
        print(f"字体处理器初始化完成，加载了 {len(self.font_mapping)} 个字体")
    
    def process_regions(self, image: np.ndarray, regions: List[TextRegion]) -> ProcessingResult:
        """
        处理文字区域进行字体匹配
        
        Args:
            image: 原始图像
            regions: 文字区域列表
            
        Returns:
            ProcessingResult: 包含FontMatchResult列表的处理结果
        """
        try:
            font_results = []
            
            for region in regions:
                if not region.is_chinese:
                    continue
                    
                # 提取文字区域
                x, y, w, h = region.bbox
                text_region = image[y:y+h, x:x+w]
                
                if text_region.size == 0:
                    continue
                
                # 进行字体匹配
                matched_font, confidence = self._match_font(text_region, region.text)
                
                # 获取字体路径
                font_path = self.font_mapping.get(matched_font, "")
                
                # 检测日文支持
                supports_japanese = self._test_japanese_support(font_path) if font_path else False
                
                # 如果不支持日文，使用思源黑体作为兜底
                if not supports_japanese:
                    fallback_font = '思源黑体'
                    fallback_path = self.font_mapping.get(fallback_font, "")
                    if fallback_path:
                        matched_font = fallback_font
                        font_path = fallback_path
                        supports_japanese = True
                        print(f"     → {region.text}: 使用兜底字体 {fallback_font}")
                
                font_result = FontMatchResult(
                    text=region.text,
                    matched_font=matched_font,
                    font_path=font_path,
                    confidence=confidence,
                    supports_japanese=supports_japanese,
                    region_id=region.id
                )
                
                font_results.append(font_result)
                
                print(f"     → {region.text}: {matched_font} (相似度: {confidence:.3f}, "
                      f"日文支持: {'是' if supports_japanese else '否'})")
            
            print(f"字体匹配完成，处理了 {len(font_results)} 个中文区域")
            return ProcessingResult.success_result(font_results)
            
        except Exception as e:
            error_msg = f"字体处理失败: {str(e)}"
            print(error_msg)
            return ProcessingResult.error_result(error_msg)
    
    def _match_font(self, text_region: np.ndarray, text: str) -> Tuple[str, float]:
        """
        匹配最相似的字体
        
        Args:
            text_region: 文字区域图像
            text: 文字内容
            
        Returns:
            Tuple[str, float]: (字体名称, 相似度)
        """
        if len(self.font_mapping) == 0:
            return "默认字体", 0.0
        
        # 预处理文字区域
        processed_region = self._preprocess_text_region(text_region)
        
        best_font = None
        best_similarity = 0.0
        
        # 遍历所有字体进行匹配
        for font_name, font_path in self.font_mapping.items():
            try:
                # 生成字体样本
                font_sample = self._generate_font_sample(text, font_path)
                if font_sample is None:
                    continue
                
                # 计算相似度
                similarity = self._calculate_similarity(processed_region, font_sample)
                
                if similarity > best_similarity:
                    best_similarity = similarity
                    best_font = font_name
                    
            except Exception as e:
                print(f"字体匹配错误 {font_name}: {e}")
                continue
        
        return best_font or "默认字体", best_similarity
    
    def _preprocess_text_region(self, region: np.ndarray) -> np.ndarray:
        """预处理文字区域"""
        if len(region.shape) == 3:
            gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
        else:
            gray = region.copy()
        
        # 调整大小
        resized = cv2.resize(gray, self.sample_size)
        
        # 二值化
        _, binary = cv2.threshold(resized, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return binary
    
    def _generate_font_sample(self, text: str, font_path: str) -> np.ndarray:
        """生成字体样本"""
        try:
            # 创建PIL图像
            img = Image.new('L', self.sample_size, 255)
            draw = ImageDraw.Draw(img)
            
            # 加载字体
            font = ImageFont.truetype(font_path, self.font_size)
            
            # 计算文字位置（居中）
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (self.sample_size[0] - text_width) // 2
            y = (self.sample_size[1] - text_height) // 2
            
            # 绘制文字
            draw.text((x, y), text, font=font, fill=0)
            
            # 转换为numpy数组
            sample = np.array(img)
            
            return sample
            
        except Exception as e:
            print(f"生成字体样本失败 {font_path}: {e}")
            return None
    
    def _calculate_similarity(self, region1: np.ndarray, region2: np.ndarray) -> float:
        """计算两个图像的相似度"""
        try:
            # 确保图像大小一致
            if region1.shape != region2.shape:
                region2 = cv2.resize(region2, (region1.shape[1], region1.shape[0]))
            
            # 使用SSIM计算结构相似性
            similarity = ssim(region1, region2)
            return max(0.0, similarity)  # 确保非负
            
        except Exception as e:
            print(f"相似度计算失败: {e}")
            return 0.0
    
    def _test_japanese_support(self, font_path: str) -> bool:
        """测试字体是否支持日文"""
        if not font_path:
            return False
            
        try:
            # 测试日文字符
            test_chars = ['あ', 'か', 'さ', 'た', 'な']  # 平假名
            
            font = ImageFont.truetype(font_path, 24)
            
            for char in test_chars:
                try:
                    # 尝试获取字符的边界框
                    img = Image.new('RGB', (50, 50), 'white')
                    draw = ImageDraw.Draw(img)
                    bbox = draw.textbbox((0, 0), char, font=font)
                    
                    # 如果边界框有效，说明字体支持该字符
                    if bbox[2] > bbox[0] and bbox[3] > bbox[1]:
                        return True
                        
                except Exception:
                    continue
            
            return False
            
        except Exception as e:
            print(f"日文支持测试失败 {font_path}: {e}")
            return False
    
    def get_font_path(self, font_name: str) -> str:
        """获取字体文件路径"""
        return self.font_mapping.get(font_name, "")

"""
文字渲染器
负责译文绘制和最终图像合成
"""
import os
import cv2
import numpy as np
from typing import List, Tuple, Dict, Any
from PIL import Image, ImageDraw, ImageFont

from models.data_models import TranslationResult, LayoutResult, RenderConfig, ProcessingResult
from config.settings import get_config_manager


class Renderer:
    """文字渲染器"""

    def __init__(self, weight_adjustment: int = 400):
        """
        初始化渲染器

        Args:
            weight_adjustment: 可变字体粗细调节值 (100-900)
        """
        self.config_manager = get_config_manager()
        self.weight_adjustment = weight_adjustment

        # 初始化布局处理器（复用实例）
        from processors.layout_processor import LayoutProcessor
        self.layout_processor = LayoutProcessor()

        print(f"渲染器初始化完成，字体粗细设置: {weight_adjustment}")
    
    def render_translations(
        self,
        base_image: np.ndarray,
        translation_results: List[TranslationResult],
        layout_result: LayoutResult
    ) -> ProcessingResult:
        """
        渲染翻译文字到图像上
        
        Args:
            base_image: 基础图像（已去除原文字）
            translation_results: 翻译结果列表
            layout_result: 布局分析结果
            
        Returns:
            ProcessingResult: 包含最终图像和渲染日志的结果
        """
        try:
            print(f"开始渲染 {len(translation_results)} 个翻译文字")
            
            # 转换为PIL图像进行文字渲染
            pil_image = Image.fromarray(cv2.cvtColor(base_image, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_image)
            
            render_log = []
            
            # 计算每个翻译的渲染配置
            render_configs = self._calculate_render_configs(
                translation_results, layout_result
            )
            
            # 渲染每个翻译文字
            for result, config in zip(translation_results, render_configs):
                try:
                    self._render_single_text(draw, result, config)
                    
                    render_log.append({
                        'original': result.original_text,
                        'translated': result.translated_text,
                        'font': result.font_info.matched_font,
                        'position': (config.text_x, config.text_y),
                        'size': config.font_size
                    })
                    
                    print(f"已渲染: '{result.original_text}' → '{result.translated_text}' "
                          f"at ({config.text_x}, {config.text_y})")
                    
                except Exception as e:
                    print(f"渲染单个文字失败 '{result.original_text}': {e}")
                    continue
            
            # 转换回OpenCV格式
            final_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            result_data = {
                'image': final_image,
                'render_log': render_log
            }
            
            print(f"渲染完成，成功渲染 {len(render_log)} 个文字")
            return ProcessingResult.success_result(result_data)
            
        except Exception as e:
            error_msg = f"渲染失败: {str(e)}"
            print(error_msg)
            return ProcessingResult.error_result(error_msg)
    
    def _calculate_render_configs(
        self,
        translation_results: List[TranslationResult],
        layout_result: LayoutResult
    ) -> List[RenderConfig]:
        """计算渲染配置"""
        configs = []
        
        for result in translation_results:
            try:
                config = self._calculate_single_render_config(result, layout_result)
                configs.append(config)
            except Exception as e:
                print(f"计算渲染配置失败 '{result.original_text}': {e}")
                # 使用默认配置
                x, y, w, h = result.bbox
                configs.append(RenderConfig(
                    text_x=x,
                    text_y=y,
                    text_width=w,
                    text_height=h,
                    font_size=result.style_info.estimated_font_size,
                    font_path=result.font_info.font_path,
                    alignment_type='center',
                    color=result.style_info.color
                ))
        
        return configs

    def _calculate_single_render_config(
        self,
        result: TranslationResult,
        layout_result: LayoutResult
    ) -> RenderConfig:
        """计算单个文字的渲染配置"""
        x, y, w, h = result.bbox

        # 计算字体大小
        font_size = self._calculate_font_size(result)

        # 获取文字尺寸
        text_width, text_height = self._get_text_dimensions(
            result.translated_text, result.font_info.font_path, font_size
        )

        # 确定对齐方式
        alignment_type = self._determine_alignment(result, layout_result)

        # 计算文字位置
        text_x, text_y = self._calculate_text_position(
            x, y, w, h, text_width, text_height, alignment_type
        )

        return RenderConfig(
            text_x=text_x,
            text_y=text_y,
            text_width=text_width,
            text_height=text_height,
            font_size=font_size,
            font_path=result.font_info.font_path,
            alignment_type=alignment_type,
            color=result.style_info.color
        )

    def _calculate_font_size(self, result: TranslationResult) -> int:
        """直接使用翻译阶段计算好的字体大小"""
        # 直接使用翻译阶段的最终字号
        font_size = result.final_font_size
        print(f"    使用翻译阶段字号: '{result.original_text}' → {font_size}px")
        return font_size

    def _get_text_dimensions(self, text: str, font_path: str, font_size: int) -> Tuple[int, int]:
        """获取文字尺寸"""
        try:
            # 创建字体对象
            if self.weight_adjustment != 400:
                # 可变字体设置
                font = ImageFont.truetype(font_path, font_size)
                if hasattr(font, 'set_variation_by_axes'):
                    font.set_variation_by_axes([self.weight_adjustment])
            else:
                font = ImageFont.truetype(font_path, font_size)

            # 创建临时图像计算文字尺寸
            temp_img = Image.new('RGB', (1, 1))
            temp_draw = ImageDraw.Draw(temp_img)
            bbox = temp_draw.textbbox((0, 0), text, font=font)

            width = bbox[2] - bbox[0]
            height = bbox[3] - bbox[1]

            return width, height

        except Exception as e:
            print(f"获取文字尺寸失败: {e}")
            # 返回估算尺寸
            return len(text) * font_size // 2, font_size

    def _determine_alignment(self, result: TranslationResult, layout_result) -> str:
        """确定对齐方式"""
        try:
            # 使用布局处理器的方法来确定对齐方式
            return self.layout_processor.get_alignment_for_region(result.bbox, layout_result)

        except Exception as e:
            print(f"确定对齐方式失败: {e}")
            return 'center'

    def _calculate_text_position(
        self,
        orig_x: int, orig_y: int, orig_w: int, orig_h: int,
        text_width: int, text_height: int,
        alignment_type: str
    ) -> Tuple[int, int]:
        """计算文字位置"""
        # 垂直居中
        text_y = orig_y + (orig_h - text_height) // 2

        # 水平对齐
        if alignment_type == 'left':
            text_x = orig_x
        elif alignment_type == 'right':
            text_x = orig_x + orig_w - text_width
        else:  # 'center'
            text_x = orig_x + (orig_w - text_width) // 2

        # 确保位置不为负数
        text_x = max(0, text_x)
        text_y = max(0, text_y)

        return text_x, text_y

    def _render_single_text(
        self,
        draw: ImageDraw.Draw,
        result: TranslationResult,
        config: RenderConfig
    ):
        """渲染单个文字"""
        try:
            # 创建字体对象
            if self.weight_adjustment != 400:
                # 可变字体设置
                font = ImageFont.truetype(config.font_path, config.font_size)
                if hasattr(font, 'set_variation_by_axes'):
                    font.set_variation_by_axes([self.weight_adjustment])
            else:
                font = ImageFont.truetype(config.font_path, config.font_size)

            # 绘制文字
            draw.text(
                (config.text_x, config.text_y),
                result.translated_text,
                font=font,
                fill=config.color
            )

        except Exception as e:
            print(f"渲染文字失败 '{result.translated_text}': {e}")
            # 尝试使用默认字体
            try:
                default_font = ImageFont.load_default()
                draw.text(
                    (config.text_x, config.text_y),
                    result.translated_text,
                    font=default_font,
                    fill=config.color
                )
            except Exception as e2:
                print(f"使用默认字体渲染也失败: {e2}")

    def save_final_image(self, image: np.ndarray, filename: str = "final_translated.png") -> str:
        """
        保存最终图像

        Args:
            image: 最终图像
            filename: 文件名

        Returns:
            str: 保存的文件路径
        """
        try:
            output_dir = self.config_manager.ensure_output_dir()
            output_path = f"{output_dir}/{filename}"

            cv2.imwrite(output_path, image)
            print(f"最终翻译结果已保存: {output_path}")

            return output_path

        except Exception as e:
            print(f"保存最终图像失败: {e}")
            return ""

"""
文字渲染器
负责译文绘制和最终图像合成
"""
import os
import cv2
import numpy as np
from typing import List, Tuple, Dict, Any
from PIL import Image, ImageDraw, ImageFont

from models.data_models import TranslationResult, LayoutResult, RenderConfig, ProcessingResult
from config.settings import get_config_manager


class Renderer:
    """文字渲染器"""

    def __init__(self, weight_adjustment: int = 400):
        """
        初始化渲染器

        Args:
            weight_adjustment: 可变字体粗细调节值 (100-900)
        """
        self.config_manager = get_config_manager()
        self.weight_adjustment = weight_adjustment
        
        # 字号缓存：(目标高度, 字体路径) -> 字号
        self._font_size_cache = {}

        # 初始化布局处理器（复用实例）
        from processors.layout_processor import LayoutProcessor
        self.layout_processor = LayoutProcessor()

        print(f"渲染器初始化完成，字体粗细设置: {weight_adjustment}")
    
    def render_translations(
        self,
        base_image: np.ndarray,
        translation_results: List[TranslationResult],
        layout_result: LayoutResult
    ) -> ProcessingResult:
        """
        渲染翻译文字到图像上
        
        Args:
            base_image: 基础图像（已去除原文字）
            translation_results: 翻译结果列表
            layout_result: 布局分析结果
            
        Returns:
            ProcessingResult: 包含最终图像和渲染日志的结果
        """
        try:
            print(f"开始渲染 {len(translation_results)} 个翻译文字")
            
            # 转换为PIL图像进行文字渲染
            pil_image = Image.fromarray(cv2.cvtColor(base_image, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_image)
            
            render_log = []
            
            # 计算每个翻译的渲染配置
            render_configs = self._calculate_render_configs(
                translation_results, layout_result
            )
            
            # 渲染每个翻译文字
            for result, config in zip(translation_results, render_configs):
                try:
                    self._render_single_text(draw, result, config)
                    
                    render_log.append({
                        'original': result.original_text,
                        'translated': result.translated_text,
                        'font': result.font_info.matched_font,
                        'position': (config.text_x, config.text_y),
                        'size': config.font_size
                    })
                    
                    print(f"已渲染: '{result.original_text}' → '{result.translated_text}' "
                          f"at ({config.text_x}, {config.text_y})")
                    
                except Exception as e:
                    print(f"渲染单个文字失败 '{result.original_text}': {e}")
                    continue
            
            # 转换回OpenCV格式
            final_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            
            result_data = {
                'image': final_image,
                'render_log': render_log
            }
            
            print(f"渲染完成，成功渲染 {len(render_log)} 个文字")
            return ProcessingResult.success_result(result_data)
            
        except Exception as e:
            error_msg = f"渲染失败: {str(e)}"
            print(error_msg)
            return ProcessingResult.error_result(error_msg)
    
    def _calculate_render_configs(
        self,
        translation_results: List[TranslationResult],
        layout_result: LayoutResult
    ) -> List[RenderConfig]:
        """计算渲染配置"""
        configs = []
        
        for result in translation_results:
            try:
                config = self._calculate_single_render_config(result, layout_result)
                configs.append(config)
            except Exception as e:
                print(f"计算渲染配置失败 '{result.original_text}': {e}")
                # 使用默认配置
                x, y, w, h = result.bbox
                configs.append(RenderConfig(
                    text_x=x,
                    text_y=y,
                    text_width=w,
                    text_height=h,
                    font_size=result.style_info.estimated_font_size,
                    font_path=result.font_info.font_path,
                    alignment_type='center',
                    color=result.style_info.color
                ))
        
        return configs

    def _calculate_single_render_config(
        self,
        result: TranslationResult,
        layout_result: LayoutResult
    ) -> RenderConfig:
        """计算单个文字的渲染配置"""
        x, y, w, h = result.bbox

        # 计算字体大小
        font_size = self._calculate_font_size(result)

        # 获取文字尺寸
        text_width, text_height = self._get_text_dimensions(
            result.translated_text, result.font_info.font_path, font_size
        )

        # 确定对齐方式
        alignment_type = self._determine_alignment(result, layout_result)

        # 计算文字位置
        text_x, text_y = self._calculate_text_position(
            x, y, w, h, text_width, text_height, alignment_type
        )

        return RenderConfig(
            text_x=text_x,
            text_y=text_y,
            text_width=text_width,
            text_height=text_height,
            font_size=font_size,
            font_path=result.font_info.font_path,
            alignment_type=alignment_type,
            color=result.style_info.color
        )

    def _calculate_font_size(self, result: TranslationResult) -> int:
        """计算字体大小 - 使用精确高度匹配"""
        try:
            # 获取精确的目标高度
            if hasattr(result.style_info, 'precise_height'):
                target_height = result.style_info.precise_height
            else:
                # 兜底：使用估算高度
                target_height = result.style_info.estimated_font_size

            # 应用分组缩放因子
            target_height = int(target_height * result.group_scale_factor)

            # 使用精确字号匹配算法
            font_size = self._calculate_font_size_by_height_matching(
                result.translated_text,
                target_height,
                result.font_info.font_path,
                result.style_info.estimated_font_size
            )

            # 确保字体大小在合理范围内
            min_size, max_size = self.config_manager.config.font_size_range
            return max(min_size, min(max_size, font_size))

        except Exception as e:
            print(f"精确字号计算失败: {e}")
            # 兜底到简单计算
            base_size = result.style_info.estimated_font_size
            scale_factor = result.group_scale_factor
            adjusted_size = int(base_size * scale_factor)
            min_size, max_size = self.config_manager.config.font_size_range
            return max(min_size, min(max_size, adjusted_size))

    def _get_text_dimensions(self, text: str, font_path: str, font_size: int) -> Tuple[int, int]:
        """获取文字尺寸"""
        try:
            # 创建字体对象
            if self.weight_adjustment != 400:
                # 可变字体设置
                font = ImageFont.truetype(font_path, font_size)
                if hasattr(font, 'set_variation_by_axes'):
                    font.set_variation_by_axes([self.weight_adjustment])
            else:
                font = ImageFont.truetype(font_path, font_size)

            # 创建临时图像计算文字尺寸
            temp_img = Image.new('RGB', (1, 1))
            temp_draw = ImageDraw.Draw(temp_img)
            bbox = temp_draw.textbbox((0, 0), text, font=font)

            width = bbox[2] - bbox[0]
            height = bbox[3] - bbox[1]

            return width, height

        except Exception as e:
            print(f"获取文字尺寸失败: {e}")
            # 返回估算尺寸
            return len(text) * font_size // 2, font_size

    def _determine_alignment(self, result: TranslationResult, layout_result) -> str:
        """确定对齐方式"""
        try:
            # 使用布局处理器的方法来确定对齐方式
            return self.layout_processor.get_alignment_for_region(result.bbox, layout_result)

        except Exception as e:
            print(f"确定对齐方式失败: {e}")
            return 'center'

    def _calculate_text_position(
        self,
        orig_x: int, orig_y: int, orig_w: int, orig_h: int,
        text_width: int, text_height: int,
        alignment_type: str
    ) -> Tuple[int, int]:
        """计算文字位置"""
        # 垂直居中
        text_y = orig_y + (orig_h - text_height) // 2

        # 水平对齐
        if alignment_type == 'left':
            text_x = orig_x
        elif alignment_type == 'right':
            text_x = orig_x + orig_w - text_width
        else:  # 'center'
            text_x = orig_x + (orig_w - text_width) // 2

        # 确保位置不为负数
        text_x = max(0, text_x)
        text_y = max(0, text_y)

        return text_x, text_y

    def _render_single_text(
        self,
        draw: ImageDraw.Draw,
        result: TranslationResult,
        config: RenderConfig
    ):
        """渲染单个文字"""
        try:
            # 创建字体对象
            if self.weight_adjustment != 400:
                # 可变字体设置
                font = ImageFont.truetype(config.font_path, config.font_size)
                if hasattr(font, 'set_variation_by_axes'):
                    font.set_variation_by_axes([self.weight_adjustment])
            else:
                font = ImageFont.truetype(config.font_path, config.font_size)

            # 绘制文字
            draw.text(
                (config.text_x, config.text_y),
                result.translated_text,
                font=font,
                fill=config.color
            )

        except Exception as e:
            print(f"渲染文字失败 '{result.translated_text}': {e}")
            # 尝试使用默认字体
            try:
                default_font = ImageFont.load_default()
                draw.text(
                    (config.text_x, config.text_y),
                    result.translated_text,
                    font=default_font,
                    fill=config.color
                )
            except Exception as e2:
                print(f"使用默认字体渲染也失败: {e2}")

    def save_final_image(self, image: np.ndarray, filename: str = "final_translated.png") -> str:
        """
        保存最终图像

        Args:
            image: 最终图像
            filename: 文件名

        Returns:
            str: 保存的文件路径
        """
        try:
            output_dir = self.config_manager.ensure_output_dir()
            output_path = f"{output_dir}/{filename}"

            cv2.imwrite(output_path, image)
            print(f"最终翻译结果已保存: {output_path}")

            return output_path

        except Exception as e:
            print(f"保存最终图像失败: {e}")
            return ""

    def _calculate_font_size_by_height_matching(
        self,
        text: str,
        target_height: int,
        font_path: str,
        initial_guess: int = None
    ) -> int:
        """
        通过实际渲染高度匹配来计算字体大小

        Args:
            text: 要渲染的文字
            target_height: 目标高度（像素）
            font_path: 字体文件路径
            initial_guess: 初始估计字号

        Returns:
            int: 匹配的字体大小
        """
        if not font_path or not os.path.exists(font_path):
            return initial_guess or 20
        
        # 检查缓存
        cache_key = (target_height, font_path)
        if cache_key in self._font_size_cache:
            cached_size = self._font_size_cache[cache_key]
            print(f"    使用缓存字号: 目标高度{target_height}px → {cached_size}px")
            return cached_size

        print(f"    开始像素级高度匹配: 目标高度{target_height}px")

        # 设置搜索范围
        min_size, max_size = 8, 100
        target_tolerance = 0  # 精确匹配：差值必须为0

        # 如果有初始估计，从该点开始二分搜索
        if initial_guess:
            test_size = initial_guess
        else:
            test_size = (min_size + max_size) // 2

        best_size = test_size
        best_diff = float('inf')
        iteration = 0
        max_iterations = 12  # 避免无限循环

        while iteration < max_iterations:
            try:
                # 使用实际渲染测量高度
                actual_height = self._measure_rendered_text_height(text, font_path, test_size)
                height_diff = abs(actual_height - target_height)

                print(f"      字体{test_size}px: 渲染高度{actual_height}px vs 目标{target_height}px (差值{height_diff})")

                # 记录最佳匹配
                if height_diff < best_diff:
                    best_diff = height_diff
                    best_size = test_size

                # 只有差值为0才算精确匹配
                if height_diff == 0:
                    print(f"      ✓ 找到精确匹配: {test_size}px")
                    # 保存到缓存
                    self._font_size_cache[cache_key] = test_size
                    return test_size

                # 二分搜索调整
                if actual_height < target_height:
                    # 实际高度太小，需要增大字体
                    min_size = test_size + 1
                else:
                    # 实际高度太大，需要减小字体
                    max_size = test_size - 1

                # 检查搜索范围是否有效
                if min_size > max_size:
                    break

                test_size = (min_size + max_size) // 2
                iteration += 1

            except Exception as e:
                print(f"      渲染测试失败: {e}")
                break

        print(f"      最终选择: {best_size}px (渲染高度差{best_diff:.1f}px)")
        # 保存到缓存
        self._font_size_cache[cache_key] = best_size
        return best_size

    def _measure_rendered_text_height(self, text: str, font_path: str, font_size: int) -> int:
        """
        测量渲染文字的实际高度

        Args:
            text: 要测量的文字
            font_path: 字体文件路径
            font_size: 字体大小

        Returns:
            int: 实际渲染高度（像素）
        """
        try:
            # 创建测试画布
            sample_width, sample_height = 400, 200
            test_image = Image.new('RGB', (sample_width, sample_height), 'white')
            draw = ImageDraw.Draw(test_image)

            # 创建字体对象
            if self.weight_adjustment != 400:
                # 可变字体设置
                font = ImageFont.truetype(font_path, font_size)
                if hasattr(font, 'set_variation_by_axes'):
                    font.set_variation_by_axes([self.weight_adjustment])
            else:
                font = ImageFont.truetype(font_path, font_size)

            # 在画布中心渲染文字
            text_x = sample_width // 4
            text_y = sample_height // 4
            draw.text((text_x, text_y), text, font=font, fill=(0, 0, 0))

            # 转换为numpy数组进行像素分析
            img_array = np.array(test_image)
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)

            # 二值化找到文字像素
            _, binary = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY_INV)

            # 找到文字区域的边界框
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if contours:
                # 获取最大轮廓（应该是我们的文字）
                largest_contour = max(contours, key=cv2.contourArea)
                _, _, _, h = cv2.boundingRect(largest_contour)
                return h
            else:
                # 如果找不到轮廓，回退到字体度量
                bbox = font.getbbox(text)
                return bbox[3] - bbox[1]

        except Exception:
            # 出错时回退到字体度量
            try:
                font = ImageFont.truetype(font_path, font_size)
                bbox = font.getbbox(text)
                return bbox[3] - bbox[1]
            except:
                return font_size

"""
布局分析处理器
负责文本布局模式识别和对齐策略分析
"""
import cv2
import os
import json
import numpy as np
from typing import List, Dict, Any, Tuple
from collections import defaultdict
import math
from PIL import Image, ImageDraw, ImageFont

from models.data_models import TextRegion, LayoutResult, ProcessingResult
from config.settings import get_config_manager


class LayoutProcessor:
    """布局分析处理器"""

    # 调试颜色常量
    DEBUG_COLORS = {
        'left_aligned': (0, 255, 0),      # 绿色 - 左对齐组
        'center_aligned': (0, 0, 255),    # 蓝色 - 居中对齐组
        'right_aligned': (255, 0, 0),     # 红色 - 右对齐组
        'distribution': (255, 0, 255),    # 紫色 - 分布组
        'ungrouped': (255, 165, 0),       # 橙色 - 未分组区域
        'alignment_line': (255, 255, 0),  # 黄色 - 对齐基准线
        'region_border': (173, 216, 230), # 浅蓝色 - 区域边框
        'grid_line': (200, 200, 200)      # 浅灰色 - 网格线
    }

    def __init__(self):
        """初始化布局分析处理器"""
        self.config_manager = get_config_manager()
        self.alignment_threshold = self.config_manager.config.alignment_threshold
        self.proximity_threshold = self.config_manager.config.proximity_threshold
        print(f"布局分析处理器初始化完成 (对齐阈值: {self.alignment_threshold}px, 邻近阈值: {self.proximity_threshold}px)")
    
    def analyze_layout(self, chinese_regions: List[TextRegion]) -> ProcessingResult:
        """分析文本布局"""
        try:
            print(f"布局分析: {len(chinese_regions)} 个中文区域")

            if len(chinese_regions) < 2:
                layout_result = self._create_simple_layout_result(chinese_regions)
            else:
                layout_result = self._analyze_complex_layout(chinese_regions)

            self._print_layout_summary(layout_result)

            # 生成调试图像和数据（如果启用）
            if self.config_manager.config.enable_layout_debug:
                self._generate_debug_outputs(chinese_regions, layout_result)

            return ProcessingResult.success_result(layout_result)

        except Exception as e:
            error_msg = f"布局分析失败: {str(e)}"
            print(error_msg)
            return ProcessingResult.error_result(error_msg)

    def _analyze_complex_layout(self, chinese_regions: List[TextRegion]) -> LayoutResult:
        """分析复杂布局"""
        # 转换为内部数据格式
        regions_data = self._convert_regions_to_dict(chinese_regions)

        # 核心分析步骤
        horizontal_alignment = self._detect_horizontal_alignment(regions_data)
        vertical_distribution = self._analyze_vertical_distribution(regions_data)
        spacing_pattern = self._analyze_spacing_pattern(regions_data)
        layout_mode = self._identify_layout_mode(regions_data, horizontal_alignment, vertical_distribution, spacing_pattern)
        alignment_strategies = self._generate_alignment_strategies(layout_mode, horizontal_alignment, vertical_distribution)

        return LayoutResult(
            layout_mode=layout_mode,
            regions=regions_data,
            horizontal_alignment=horizontal_alignment,
            vertical_distribution=vertical_distribution,
            alignment_strategies=alignment_strategies
        )
    
    def _convert_regions_to_dict(self, regions: List[TextRegion]) -> List[Dict[str, Any]]:
        """将TextRegion转换为字典格式"""
        regions_data = []
        for region in regions:
            x, y, w, h = region.bbox
            regions_data.append({
                'id': region.id,
                'text': region.text,
                'bbox': region.bbox,
                'center': region.center,
                'left': x,
                'right': x + w,
                'top': y,
                'bottom': y + h
            })
        return regions_data
    
    def _detect_horizontal_alignment(self, regions: List[Dict]) -> Dict[str, Any]:
        """检测水平对齐模式 - 改进版：先按行分组，再分析行内对齐"""
        if len(regions) < 2:
            return {'type': 'single', 'left_groups': [], 'center_groups': [], 'right_groups': [], 'distribution_groups': []}
        
        # 第一步：按Y坐标将区域分成行
        row_groups = self._group_regions_by_rows(regions)
        
        # 第二步：分析每行内部的对齐关系
        all_left_groups = []
        all_center_groups = []
        all_right_groups = []
        all_distribution_groups = []
        
        for row_regions in row_groups:
            if len(row_regions) < 2:
                continue  # 单个区域的行不需要分析对齐
            
            # 在行内分析严格对齐关系
            row_left_groups = self._find_aligned_groups_in_row(row_regions, 'left')
            row_center_groups = self._find_aligned_groups_in_row(row_regions, 'center')
            row_right_groups = self._find_aligned_groups_in_row(row_regions, 'right')
            
            all_left_groups.extend(row_left_groups)
            all_center_groups.extend(row_center_groups)
            all_right_groups.extend(row_right_groups)
            
            # 如果行内没有找到严格对齐组，将整行作为分布组
            has_alignment = bool(row_left_groups or row_center_groups or row_right_groups)
            if not has_alignment and len(row_regions) >= 2:
                all_distribution_groups.append(row_regions)
        
        # 第三步：跨行检测对齐（原有逻辑）
        cross_row_left_groups = self._find_cross_row_aligned_groups(regions, 'left')
        cross_row_center_groups = self._find_cross_row_aligned_groups(regions, 'center')
        cross_row_right_groups = self._find_cross_row_aligned_groups(regions, 'right')
        
        # 合并行内和跨行的对齐组
        all_left_groups.extend(cross_row_left_groups)
        all_center_groups.extend(cross_row_center_groups)
        all_right_groups.extend(cross_row_right_groups)
        
        # 确定主要对齐模式
        alignment_type = self._determine_primary_alignment(all_left_groups, all_center_groups, all_right_groups)
        
        # 如果没有找到明确的对齐模式，但有分布组，则标记为分布式
        if alignment_type == 'mixed' and all_distribution_groups:
            alignment_type = 'distributed'
        
        return {
            'type': alignment_type,
            'left_groups': all_left_groups,
            'center_groups': all_center_groups,
            'right_groups': all_right_groups,
            'distribution_groups': all_distribution_groups
        }
    
    def _group_regions_by_rows(self, regions: List[Dict]) -> List[List[Dict]]:
        """按Y坐标将区域分成行"""
        if not regions:
            return []
        
        # 按Y坐标排序
        sorted_regions = sorted(regions, key=lambda r: r['top'])
        
        rows = []
        current_row = [sorted_regions[0]]
        
        for i in range(1, len(sorted_regions)):
            current_region = sorted_regions[i]
            last_region = current_row[-1]
            
            # 检查是否在同一行（Y坐标相近）
            y_diff = abs(current_region['top'] - last_region['top'])
            if y_diff <= self.alignment_threshold:
                current_row.append(current_region)
            else:
                rows.append(current_row)
                current_row = [current_region]
        
        rows.append(current_row)
        return rows
    
    def _find_aligned_groups_in_row(self, row_regions: List[Dict], align_type: str) -> List[List[Dict]]:
        """在单行内查找对齐组"""
        if len(row_regions) < 2:
            return []
        
        # 对于行内对齐，我们需要更宽松的判断
        # 因为同一行的文字可能是左右分布，而不是严格对齐
        
        if align_type == 'left':
            # 检查是否有多个区域的左边界接近
            return self._find_aligned_groups(row_regions, 'left')
        elif align_type == 'right':
            # 检查是否有多个区域的右边界接近
            return self._find_aligned_groups(row_regions, 'right')
        elif align_type == 'center':
            # 检查是否有多个区域的中心接近
            return self._find_aligned_groups(row_regions, 'center')
        else:
            return []
    
    def _find_cross_row_aligned_groups(self, regions: List[Dict], align_type: str) -> List[List[Dict]]:
        """查找跨行的对齐组（原有逻辑）"""
        return self._find_aligned_groups(regions, align_type)
    
    def _find_aligned_groups(self, regions: List[Dict], align_type: str) -> List[List[Dict]]:
        """查找对齐组"""
        if align_type == 'left':
            key_func = lambda r: r['left']
        elif align_type == 'right':
            key_func = lambda r: r['right']
        else:  # center
            key_func = lambda r: r['center'][0]
        
        # 按对齐位置分组
        position_groups = defaultdict(list)
        for region in regions:
            pos = key_func(region)
            position_groups[pos].append(region)
        
        # 合并相近位置的组
        aligned_groups = []
        sorted_positions = sorted(position_groups.keys())
        
        current_group = []
        for pos in sorted_positions:
            if not current_group:
                current_group.extend(position_groups[pos])
            else:
                # 检查是否与当前组的位置接近
                last_pos = key_func(current_group[-1])
                if abs(pos - last_pos) <= self.alignment_threshold:
                    current_group.extend(position_groups[pos])
                else:
                    if len(current_group) >= 2:
                        aligned_groups.append(current_group)
                    current_group = list(position_groups[pos])
        
        # 添加最后一组
        if len(current_group) >= 2:
            aligned_groups.append(current_group)
        
        return aligned_groups
    
    def _determine_primary_alignment(self, left_groups, center_groups, right_groups) -> str:
        """确定主要对齐模式"""
        left_count = sum(len(group) for group in left_groups)
        center_count = sum(len(group) for group in center_groups)
        right_count = sum(len(group) for group in right_groups)
        
        if left_count > center_count and left_count > right_count:
            return 'left'
        elif right_count > center_count and right_count > left_count:
            return 'right'
        elif center_count > 0:
            return 'center'
        else:
            return 'mixed'
    
    def _analyze_vertical_distribution(self, regions: List[Dict]) -> Dict[str, Any]:
        """分析垂直分布模式"""
        if len(regions) < 2:
            return {'type': 'single', 'rows': 1, 'columns': 1}
        
        # 按Y坐标分组找行
        y_positions = [r['top'] for r in regions]
        y_groups = self._group_by_proximity(y_positions, self.proximity_threshold)
        rows = len(y_groups)
        
        # 按X坐标分组找列
        x_positions = [r['left'] for r in regions]
        x_groups = self._group_by_proximity(x_positions, self.proximity_threshold)
        columns = len(x_groups)
        
        # 确定分布类型
        if rows == 1 and columns > 1:
            dist_type = 'horizontal'
        elif rows > 1 and columns == 1:
            dist_type = 'vertical'
        elif rows == 2 and columns == 2:
            dist_type = 'grid_2x2'
        elif rows > 2 or columns > 2:
            dist_type = 'complex'
        else:
            dist_type = 'scattered'
        
        return {
            'type': dist_type,
            'rows': rows,
            'columns': columns,
            'row_groups': y_groups,
            'column_groups': x_groups
        }
    
    def _group_by_proximity(self, positions: List[int], threshold: int) -> List[List[int]]:
        """按邻近度分组"""
        if not positions:
            return []
        
        sorted_positions = sorted(set(positions))
        groups = []
        current_group = [sorted_positions[0]]
        
        for i in range(1, len(sorted_positions)):
            if sorted_positions[i] - sorted_positions[i-1] <= threshold:
                current_group.append(sorted_positions[i])
            else:
                groups.append(current_group)
                current_group = [sorted_positions[i]]
        
        groups.append(current_group)
        return groups
    
    def _analyze_spacing_pattern(self, regions: List[Dict]) -> Dict[str, Any]:
        """分析间距模式"""
        if len(regions) < 2:
            return {'type': 'none'}
        
        # 计算水平间距
        h_spacings = []
        for i in range(len(regions)):
            for j in range(i + 1, len(regions)):
                r1, r2 = regions[i], regions[j]
                # 只计算同一行的间距
                if abs(r1['top'] - r2['top']) <= self.alignment_threshold:
                    spacing = abs(r1['center'][0] - r2['center'][0])
                    h_spacings.append(spacing)
        
        # 计算垂直间距
        v_spacings = []
        for i in range(len(regions)):
            for j in range(i + 1, len(regions)):
                r1, r2 = regions[i], regions[j]
                # 只计算同一列的间距
                if abs(r1['center'][0] - r2['center'][0]) <= self.alignment_threshold:
                    spacing = abs(r1['center'][1] - r2['center'][1])
                    v_spacings.append(spacing)
        
        # 分析间距规律性
        h_regularity = self._analyze_spacing_regularity(h_spacings)
        v_regularity = self._analyze_spacing_regularity(v_spacings)
        
        return {
            'type': 'regular' if h_regularity == 'regular' and v_regularity == 'regular' else 'irregular',
            'horizontal_regularity': h_regularity,
            'vertical_regularity': v_regularity,
            'horizontal_spacings': h_spacings,
            'vertical_spacings': v_spacings
        }
    
    def _analyze_spacing_regularity(self, spacings: List[int]) -> str:
        """分析间距规律性"""
        if len(spacings) < 2:
            return 'insufficient_data'
        
        # 计算变异系数
        mean_spacing = np.mean(spacings)
        std_spacing = np.std(spacings)
        
        if mean_spacing == 0:
            return 'zero_spacing'
        
        cv = std_spacing / mean_spacing
        
        if cv < 0.2:
            return 'regular'
        elif cv < 0.5:
            return 'semi_regular'
        else:
            return 'irregular'

    def _identify_layout_mode(
        self,
        regions: List[Dict],
        h_align: Dict,
        v_dist: Dict,
        spacing: Dict
    ) -> str:
        """识别整体布局模式"""
        num_regions = len(regions)

        if num_regions == 1:
            return 'single_text'
        elif num_regions == 2:
            return 'dual_text'

        # 基于垂直分布判断
        if v_dist['type'] == 'horizontal':
            if h_align['type'] == 'left':
                return 'horizontal_left_aligned'
            elif h_align['type'] == 'center':
                return 'horizontal_center_aligned'
            elif h_align['type'] == 'right':
                return 'horizontal_right_aligned'
            else:
                return 'horizontal_mixed'
        elif v_dist['type'] == 'vertical':
            if h_align['type'] == 'left':
                return 'vertical_left_aligned'
            elif h_align['type'] == 'center':
                return 'vertical_center_aligned'
            else:
                return 'vertical_mixed'
        elif v_dist['type'] == 'grid_2x2':
            return 'grid_2x2'
        elif v_dist['type'] == 'complex':
            return 'complex_layout'
        else:
            return 'scattered_layout'

    def _generate_alignment_strategies(
        self,
        layout_mode: str,
        h_align: Dict,
        v_dist: Dict
    ) -> List[Dict[str, Any]]:
        """生成对齐策略建议"""
        strategies = []

        if layout_mode == 'horizontal_left_aligned':
            strategies.append({
                'type': 'preserve_left_alignment',
                'description': '保持左对齐布局',
                'regions': 'all',
                'priority': 'high'
            })
        elif layout_mode == 'horizontal_center_aligned':
            strategies.append({
                'type': 'preserve_center_alignment',
                'description': '保持居中对齐布局',
                'regions': 'all',
                'priority': 'high'
            })
        elif layout_mode == 'vertical_left_aligned':
            strategies.append({
                'type': 'preserve_vertical_left_alignment',
                'description': '保持垂直左对齐布局',
                'regions': 'all',
                'priority': 'high'
            })
        elif layout_mode == 'grid_2x2':
            strategies.append({
                'type': 'preserve_grid_layout',
                'description': '保持2x2网格布局',
                'regions': 'all',
                'priority': 'high'
            })
        elif layout_mode == 'complex_layout':
            # 复杂布局，提供多种策略
            if len(h_align['left_groups']) > 0:
                strategies.append({
                    'type': 'group_left_alignment',
                    'description': '对左对齐组保持左对齐',
                    'regions': 'left_aligned_groups',
                    'priority': 'medium'
                })
            if len(h_align['center_groups']) > 0:
                strategies.append({
                    'type': 'group_center_alignment',
                    'description': '对居中组保持居中对齐',
                    'regions': 'center_aligned_groups',
                    'priority': 'medium'
                })
            if len(h_align['right_groups']) > 0:
                strategies.append({
                    'type': 'group_right_alignment',
                    'description': '对右对齐组保持右对齐',
                    'regions': 'right_aligned_groups',
                    'priority': 'medium'
                })
            if len(h_align.get('distribution_groups', [])) > 0:
                strategies.append({
                    'type': 'preserve_distribution',
                    'description': '保持分布组的相对位置',
                    'regions': 'distribution_groups',
                    'priority': 'high'
                })
        else:
            # 默认策略
            strategies.append({
                'type': 'center_alignment',
                'description': '使用居中对齐（默认策略）',
                'regions': 'all',
                'priority': 'low'
            })

        return strategies

    def _create_simple_layout_result(self, regions: List[TextRegion]) -> LayoutResult:
        """创建简单布局结果"""
        regions_data = self._convert_regions_to_dict(regions)

        return LayoutResult(
            layout_mode='single_text' if len(regions) <= 1 else 'simple',
            regions=regions_data,
            horizontal_alignment={'type': 'single', 'left_groups': [], 'center_groups': [], 'right_groups': [], 'distribution_groups': []},
            vertical_distribution={'type': 'single', 'rows': 1, 'columns': 1},
            alignment_strategies=[{
                'type': 'center_alignment',
                'description': '使用居中对齐',
                'regions': 'all',
                'priority': 'default'
            }]
        )

    def _print_layout_summary(self, result: LayoutResult):
        """打印布局分析摘要"""
        print(f"布局结果: {result.layout_mode} ({len(result.regions)}个区域)")
        print(f"  水平对齐: {result.horizontal_alignment['type']}")
        print(f"  垂直分布: {result.vertical_distribution['type']} "
              f"({result.vertical_distribution['rows']}行×{result.vertical_distribution['columns']}列)")

        # 打印分组信息
        h_align = result.horizontal_alignment
        group_info = []

        if h_align.get('left_groups'):
            group_info.append(f"left_group组{len(h_align['left_groups'])}个")
        if h_align.get('center_groups'):
            group_info.append(f"center_group组{len(h_align['center_groups'])}个")
        if h_align.get('right_groups'):
            group_info.append(f"right_group组{len(h_align['right_groups'])}个")
        if h_align.get('distribution_groups'):
            group_info.append(f"分布组{len(h_align['distribution_groups'])}个")

        if group_info:
            print(f"  分组: {', '.join(group_info)}")

        # 打印详细分组（仅分布组）
        for i, group in enumerate(h_align.get('distribution_groups', [])):
            texts = [region['text'] for region in group]
            print(f"    分布组{i+1}: {texts}")

    def get_alignment_for_region(self, region_bbox: Tuple[int, int, int, int], layout_result: LayoutResult) -> str:
        """
        为特定区域获取对齐方式

        Args:
            region_bbox: 区域边界框 (x, y, w, h)
            layout_result: 布局分析结果

        Returns:
            str: 对齐方式 ('left', 'center', 'right')
        """
        x, y, w, _ = region_bbox
        center_x = x + w // 2

        try:
            h_align = layout_result.horizontal_alignment

            # 检查左对齐组
            for group in h_align.get('left_groups', []):
                for region in group:
                    if (abs(region.get('left', 0) - x) <= self.alignment_threshold and
                        abs(region.get('top', 0) - y) <= self.alignment_threshold):
                        return 'left'

            # 检查右对齐组
            for group in h_align.get('right_groups', []):
                for region in group:
                    if (abs(region.get('right', 0) - (x + w)) <= self.alignment_threshold and
                        abs(region.get('top', 0) - y) <= self.alignment_threshold):
                        return 'right'

            # 检查居中对齐组
            for group in h_align.get('center_groups', []):
                for region in group:
                    region_center_x = region.get('center', [0, 0])[0]
                    if abs(region_center_x - center_x) <= self.alignment_threshold:
                        return 'center'

            # 检查分布组
            for group in h_align.get('distribution_groups', []):
                for region in group:
                    if (abs(region.get('left', 0) - x) <= self.alignment_threshold and
                        abs(region.get('top', 0) - y) <= self.alignment_threshold):
                        # 对于分布组，根据在组内的相对位置确定对齐方式
                        return self._determine_alignment_in_distribution_group(region, group)

            # 默认居中对齐
            return 'center'

        except Exception as e:
            print(f"获取区域对齐方式失败: {e}")
            return 'center'
    
    def _determine_alignment_in_distribution_group(self, target_region: Dict, group: List[Dict]) -> str:
        """在分布组内确定区域的对齐方式"""
        if len(group) < 2:
            return 'center'
        
        # 按X坐标排序
        sorted_group = sorted(group, key=lambda r: r['center'][0])
        
        # 找到目标区域在组内的位置
        target_center_x = target_region['center'][0]
        
        # 如果是最左边的区域，使用左对齐
        if target_center_x == sorted_group[0]['center'][0]:
            return 'left'
        # 如果是最右边的区域，使用右对齐
        elif target_center_x == sorted_group[-1]['center'][0]:
            return 'right'
        # 中间的区域使用居中对齐
        else:
            return 'center'

    def _generate_debug_outputs(self, chinese_regions: List[TextRegion], layout_result: LayoutResult):
        """生成调试图像和数据文件"""
        try:
            debug_dir = self.config_manager.config.layout_debug_dir
            os.makedirs(debug_dir, exist_ok=True)

            base_image = self._get_base_image(chinese_regions)

            # 生成调试图像
            self._save_alignment_groups_debug(base_image, layout_result, debug_dir)
            self._save_distribution_grid_debug(base_image, layout_result, debug_dir)
            self._save_layout_data_json(layout_result, debug_dir)

            print(f"Layout调试文件已保存到: {debug_dir}")

        except Exception as e:
            print(f"生成Layout调试文件失败: {e}")

    def _get_base_image(self, regions: List[TextRegion]) -> np.ndarray:
        """获取基础图像"""
        # 尝试加载原始图像
        try:
            image_path = "example.jpg"
            if os.path.exists(image_path):
                return cv2.imread(image_path)
        except:
            pass

        # 创建基础图像
        if not regions:
            return np.ones((600, 800, 3), dtype=np.uint8) * 255

        # 计算边界并创建图像
        all_coords = []
        for region in regions:
            x, y, w, h = region.bbox
            all_coords.extend([(x, y), (x + w, y + h)])

        min_x = min(coord[0] for coord in all_coords)
        max_x = max(coord[0] for coord in all_coords)
        min_y = min(coord[1] for coord in all_coords)
        max_y = max(coord[1] for coord in all_coords)

        margin = 50
        width = max_x - min_x + 2 * margin
        height = max_y - min_y + 2 * margin

        return np.ones((height, width, 3), dtype=np.uint8) * 255

    def _load_chinese_font(self, size=20):
        """加载中文字体"""
        try:
            return ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", size)
        except:
            try:
                font_path = os.path.join(self.config_manager.get_fonts_dir(), "思源黑体", "SourceHanSans-VF.otf")
                return ImageFont.truetype(font_path, size)
            except:
                return ImageFont.load_default()
    
    def _save_alignment_groups_debug(self, base_image: np.ndarray, layout_result: LayoutResult, debug_dir: str):
        """保存对齐组调试图像"""
        try:
            pil_image = Image.fromarray(cv2.cvtColor(base_image, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_image)
            font = self._load_chinese_font()

            grouped_region_ids = set()
            h_align = layout_result.horizontal_alignment

            # 绘制各类对齐组
            group_configs = [
                (h_align.get('left_groups', []), self.DEBUG_COLORS['left_aligned'], 'L', 'left'),
                (h_align.get('center_groups', []), self.DEBUG_COLORS['center_aligned'], 'C', 'center'),
                (h_align.get('right_groups', []), self.DEBUG_COLORS['right_aligned'], 'R', 'right'),
                (h_align.get('distribution_groups', []), self.DEBUG_COLORS['distribution'], 'D', 'distribution')
            ]

            for groups, color, prefix, align_type in group_configs:
                for group_idx, group in enumerate(groups):
                    for region in group:
                        grouped_region_ids.add(region['id'])
                        self._draw_region_box(draw, region, color, f"{prefix}{group_idx}")

                    # 绘制对齐基准线或连接线
                    if group:
                        self._draw_alignment_line(draw, group, align_type, color)

            # 绘制未分组区域
            ungrouped_regions = [r for r in layout_result.regions if r['id'] not in grouped_region_ids]
            for region in ungrouped_regions:
                self._draw_region_box(draw, region, self.DEBUG_COLORS['ungrouped'], "U")

            # 添加图例
            group_counts = [len(h_align.get(f'{t}_groups', [])) for t in ['left', 'center', 'right']]
            group_counts.extend([len(h_align.get('distribution_groups', [])), len(ungrouped_regions)])
            self._draw_legend(draw, font, group_counts)

            # 保存图像
            cv2_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            cv2.imwrite(os.path.join(debug_dir, "alignment_groups.png"), cv2_image)

        except Exception as e:
            print(f"保存对齐组调试图像失败: {e}")

    def _draw_alignment_line(self, draw: ImageDraw.Draw, group: List[Dict], align_type: str, color: tuple):
        """绘制对齐基准线或连接线"""
        if align_type == 'left':
            x = min(region['left'] for region in group)
            y_range = (min(region['top'] for region in group) - 10, max(region['bottom'] for region in group) + 10)
            draw.line([(x, y_range[0]), (x, y_range[1])], fill=self.DEBUG_COLORS['alignment_line'], width=2)
        elif align_type == 'right':
            x = max(region['right'] for region in group)
            y_range = (min(region['top'] for region in group) - 10, max(region['bottom'] for region in group) + 10)
            draw.line([(x, y_range[0]), (x, y_range[1])], fill=self.DEBUG_COLORS['alignment_line'], width=2)
        elif align_type == 'center':
            x = sum(region['center'][0] for region in group) / len(group)
            y_range = (min(region['top'] for region in group) - 10, max(region['bottom'] for region in group) + 10)
            draw.line([(x, y_range[0]), (x, y_range[1])], fill=self.DEBUG_COLORS['alignment_line'], width=2)
        elif align_type == 'distribution' and len(group) >= 2:
            y = sum(region['center'][1] for region in group) // len(group)
            x_range = (min(region['left'] for region in group), max(region['right'] for region in group))
            draw.line([(x_range[0], y), (x_range[1], y)], fill=color, width=2)
    
    def _draw_region_box(self, draw: ImageDraw.Draw, region: Dict, color: tuple, label: str):
        """绘制区域边框和标签"""
        # 绘制边框
        draw.rectangle([region['left'], region['top'], region['right'], region['bottom']],
                      outline=color, width=2)

        # 绘制标签（只显示分组类型，不包含原文）
        font = self._load_chinese_font()
        
        # 根据标签前缀确定分组类型
        if label.startswith('L'):
            label_text = f"左对齐组{label[1:]}"
        elif label.startswith('C'):
            label_text = f"居中组{label[1:]}"
        elif label.startswith('R'):
            label_text = f"右对齐组{label[1:]}"
        elif label.startswith('D'):
            label_text = f"分布组{label[1:]}"
        elif label.startswith('U'):
            label_text = "未分组"
        else:
            label_text = label

        # 计算标签位置
        try:
            bbox = draw.textbbox((0, 0), label_text, font=font)
            text_width, text_height = bbox[2] - bbox[0], bbox[3] - bbox[1]
        except:
            text_width, text_height = len(label_text) * 10, 20

        # 标签位置：左上角
        label_x = region['left']
        label_y = region['top']

        # 绘制标签背景和文字
        draw.rectangle([label_x, label_y, label_x + text_width + 4, label_y + text_height + 4], fill=color)
        draw.text((label_x + 2, label_y + 2), label_text, fill=(255, 255, 255), font=font)

    def _draw_legend(self, draw: ImageDraw.Draw, font, counts: List[int]):
        """绘制图例"""
        legend_items = [
            (f"左对齐组 ({counts[0]})", self.DEBUG_COLORS['left_aligned']),
            (f"居中对齐组 ({counts[1]})", self.DEBUG_COLORS['center_aligned']),
            (f"右对齐组 ({counts[2]})", self.DEBUG_COLORS['right_aligned']),
            (f"分布组 ({counts[3]})", self.DEBUG_COLORS['distribution']),
            (f"未分组区域 ({counts[4]})", self.DEBUG_COLORS['ungrouped']),
            ("对齐基准线", self.DEBUG_COLORS['alignment_line'])
        ]

        # 绘制图例
        for i, (text, color) in enumerate(legend_items):
            y_pos = 10 + i * 25
            draw.rectangle([10, y_pos, 25, y_pos + 15], fill=color)
            draw.text((30, y_pos), text, fill=(255, 255, 255), font=font)
    
    def _save_distribution_grid_debug(self, base_image: np.ndarray, layout_result: LayoutResult, debug_dir: str):
        """保存分布网格调试图像"""
        try:
            pil_image = Image.fromarray(cv2.cvtColor(base_image, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_image)
            font = self._load_chinese_font()

            v_dist = layout_result.vertical_distribution
            image_size = (base_image.shape[1], base_image.shape[0])

            # 绘制网格线
            self._draw_grid_lines(draw, v_dist, image_size)

            # 绘制区域
            for region in layout_result.regions:
                draw.rectangle([region['left'], region['top'], region['right'], region['bottom']],
                              outline=self.DEBUG_COLORS['region_border'], width=1)

                # 绘制标签
                label_y = region['top'] - 22 if region['top'] > 22 else region['bottom'] + 2
                draw.text((region['left'], label_y), region['text'], fill=(255, 255, 255), font=font)

            # 添加网格信息
            info_text = f"网格: {v_dist['rows']}行×{v_dist['columns']}列, 分布: {v_dist['type']}"
            draw.text((image_size[0] - 300, 10), info_text, fill=(0, 0, 0), font=font)

            # 保存图像
            final_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            cv2.imwrite(os.path.join(debug_dir, "distribution_grid.png"), final_image)

        except Exception as e:
            print(f"保存分布网格调试图像失败: {e}")

    def _draw_grid_lines(self, draw: ImageDraw.Draw, v_dist: Dict, image_size: Tuple[int, int]):
        """绘制网格线"""
        width, height = image_size

        # 绘制行线（虚线效果）
        for row_group in v_dist.get('row_groups', []):
            if row_group:
                y = sum(row_group) // len(row_group)
                for x in range(0, width, 20):
                    draw.line([(x, y), (x + 10, y)], fill=self.DEBUG_COLORS['grid_line'], width=1)

        # 绘制列线（虚线效果）
        for col_group in v_dist.get('column_groups', []):
            if col_group:
                x = sum(col_group) // len(col_group)
                for y in range(0, height, 20):
                    draw.line([(x, y), (x, y + 10)], fill=self.DEBUG_COLORS['grid_line'], width=1)
    
    def _save_layout_data_json(self, layout_result: LayoutResult, debug_dir: str):
        """保存布局数据为JSON格式"""
        layout_data = {
            "metadata": {
                "timestamp": self._get_timestamp(),
                "total_regions": len(layout_result.regions),
                "layout_mode": layout_result.layout_mode,
                "alignment_threshold": self.alignment_threshold,
                "proximity_threshold": self.proximity_threshold
            },
            "horizontal_alignment": {
                "type": layout_result.horizontal_alignment.get('type', 'unknown'),
                **{f"{group_type}_groups": [
                    [self._serialize_region(region) for region in group]
                    for group in layout_result.horizontal_alignment.get(f'{group_type}_groups', [])
                ] for group_type in ['left', 'center', 'right', 'distribution']}
            },
            "vertical_distribution": {
                "type": layout_result.vertical_distribution.get('type', 'unknown'),
                "rows": layout_result.vertical_distribution.get('rows', 0),
                "columns": layout_result.vertical_distribution.get('columns', 0),
                "row_groups": layout_result.vertical_distribution.get('row_groups', []),
                "column_groups": layout_result.vertical_distribution.get('column_groups', [])
            },
            "alignment_strategies": [
                {
                    "type": strategy.get('type', ''),
                    "description": strategy.get('description', ''),
                    "regions": strategy.get('regions', ''),
                    "priority": strategy.get('priority', '')
                }
                for strategy in layout_result.alignment_strategies
            ],
            "regions_details": [self._serialize_region(region) for region in layout_result.regions]
        }

        # 保存JSON文件
        output_path = os.path.join(debug_dir, "layout_data.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(layout_data, f, ensure_ascii=False, indent=2)

    def _serialize_region(self, region: Dict) -> Dict:
        """序列化区域数据"""
        return {
            "id": region.get('id', 0),
            "text": region.get('text', ''),
            "bbox": region.get('bbox', [0, 0, 0, 0]),
            "left": region.get('left', 0),
            "right": region.get('right', 0),
            "top": region.get('top', 0),
            "bottom": region.get('bottom', 0),
            "center": region.get('center', [0, 0])
        }

    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

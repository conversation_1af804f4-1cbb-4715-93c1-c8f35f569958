"""
布局分析处理器
负责文本布局模式识别和对齐策略分析
"""
import cv2
import os
import json
import numpy as np
from typing import List, Dict, Any, Tuple
from collections import defaultdict
import math
from PIL import Image, ImageDraw, ImageFont

from models.data_models import TextRegion, LayoutResult, ProcessingResult
from config.settings import get_config_manager


class LayoutProcessor:
    """布局分析处理器"""
    
    def __init__(self):
        """初始化布局分析处理器"""
        self.config_manager = get_config_manager()
        self.alignment_threshold = self.config_manager.config.alignment_threshold
        self.proximity_threshold = self.config_manager.config.proximity_threshold
        
        print(f"布局分析处理器初始化完成")
        print(f"  对齐阈值: {self.alignment_threshold}px")
        print(f"  邻近阈值: {self.proximity_threshold}px")
    
    def analyze_layout(self, chinese_regions: List[TextRegion]) -> ProcessingResult:
        """
        分析文本布局
        
        Args:
            chinese_regions: 中文文字区域列表
            
        Returns:
            ProcessingResult: 包含LayoutResult的处理结果
        """
        try:
            print(f"\n=== 开始布局分析 ===")
            print(f"分析 {len(chinese_regions)} 个中文文字区域")
            
            if len(chinese_regions) < 2:
                print("文字区域少于2个，使用简单布局")
                layout_result = self._create_simple_layout_result(chinese_regions)
            else:
                # 转换为内部数据格式
                regions_data = self._convert_regions_to_dict(chinese_regions)
                
                # 1. 水平对齐检测
                horizontal_alignment = self._detect_horizontal_alignment(regions_data)
                
                # 2. 垂直分布分析
                vertical_distribution = self._analyze_vertical_distribution(regions_data)
                
                # 3. 间距模式分析
                spacing_pattern = self._analyze_spacing_pattern(regions_data)
                
                # 4. 布局模式识别
                layout_mode = self._identify_layout_mode(
                    regions_data, horizontal_alignment, vertical_distribution, spacing_pattern
                )
                
                # 5. 生成对齐策略
                alignment_strategies = self._generate_alignment_strategies(
                    layout_mode, horizontal_alignment, vertical_distribution
                )
                
                # 创建布局结果
                layout_result = LayoutResult(
                    layout_mode=layout_mode,
                    regions=regions_data,
                    horizontal_alignment=horizontal_alignment,
                    vertical_distribution=vertical_distribution,
                    alignment_strategies=alignment_strategies
                )
            
            self._print_layout_analysis_result(layout_result)
            print("=== 布局分析完成 ===\n")
            
            # 生成调试图像和数据（如果启用）
            if self.config_manager.config.enable_layout_debug:
                self._generate_debug_outputs(chinese_regions, layout_result)
            
            return ProcessingResult.success_result(layout_result)
            
        except Exception as e:
            error_msg = f"布局分析失败: {str(e)}"
            print(error_msg)
            return ProcessingResult.error_result(error_msg)
    
    def _convert_regions_to_dict(self, regions: List[TextRegion]) -> List[Dict[str, Any]]:
        """将TextRegion转换为字典格式"""
        regions_data = []
        for region in regions:
            x, y, w, h = region.bbox
            regions_data.append({
                'id': region.id,
                'text': region.text,
                'bbox': region.bbox,
                'center': region.center,
                'left': x,
                'right': x + w,
                'top': y,
                'bottom': y + h
            })
        return regions_data
    
    def _detect_horizontal_alignment(self, regions: List[Dict]) -> Dict[str, Any]:
        """检测水平对齐模式 - 改进版：先按行分组，再分析行内对齐"""
        if len(regions) < 2:
            return {'type': 'single', 'left_groups': [], 'center_groups': [], 'right_groups': [], 'distribution_groups': []}
        
        # 第一步：按Y坐标将区域分成行
        row_groups = self._group_regions_by_rows(regions)
        
        # 第二步：分析每行内部的对齐关系
        all_left_groups = []
        all_center_groups = []
        all_right_groups = []
        all_distribution_groups = []
        
        for row_regions in row_groups:
            if len(row_regions) < 2:
                continue  # 单个区域的行不需要分析对齐
            
            # 在行内分析严格对齐关系
            row_left_groups = self._find_aligned_groups_in_row(row_regions, 'left')
            row_center_groups = self._find_aligned_groups_in_row(row_regions, 'center')
            row_right_groups = self._find_aligned_groups_in_row(row_regions, 'right')
            
            all_left_groups.extend(row_left_groups)
            all_center_groups.extend(row_center_groups)
            all_right_groups.extend(row_right_groups)
            
            # 如果行内没有找到严格对齐组，将整行作为分布组
            has_alignment = bool(row_left_groups or row_center_groups or row_right_groups)
            if not has_alignment and len(row_regions) >= 2:
                all_distribution_groups.append(row_regions)
        
        # 第三步：跨行检测对齐（原有逻辑）
        cross_row_left_groups = self._find_cross_row_aligned_groups(regions, 'left')
        cross_row_center_groups = self._find_cross_row_aligned_groups(regions, 'center')
        cross_row_right_groups = self._find_cross_row_aligned_groups(regions, 'right')
        
        # 合并行内和跨行的对齐组
        all_left_groups.extend(cross_row_left_groups)
        all_center_groups.extend(cross_row_center_groups)
        all_right_groups.extend(cross_row_right_groups)
        
        # 确定主要对齐模式
        alignment_type = self._determine_primary_alignment(all_left_groups, all_center_groups, all_right_groups)
        
        # 如果没有找到明确的对齐模式，但有分布组，则标记为分布式
        if alignment_type == 'mixed' and all_distribution_groups:
            alignment_type = 'distributed'
        
        return {
            'type': alignment_type,
            'left_groups': all_left_groups,
            'center_groups': all_center_groups,
            'right_groups': all_right_groups,
            'distribution_groups': all_distribution_groups
        }
    
    def _group_regions_by_rows(self, regions: List[Dict]) -> List[List[Dict]]:
        """按Y坐标将区域分成行"""
        if not regions:
            return []
        
        # 按Y坐标排序
        sorted_regions = sorted(regions, key=lambda r: r['top'])
        
        rows = []
        current_row = [sorted_regions[0]]
        
        for i in range(1, len(sorted_regions)):
            current_region = sorted_regions[i]
            last_region = current_row[-1]
            
            # 检查是否在同一行（Y坐标相近）
            y_diff = abs(current_region['top'] - last_region['top'])
            if y_diff <= self.alignment_threshold:
                current_row.append(current_region)
            else:
                rows.append(current_row)
                current_row = [current_region]
        
        rows.append(current_row)
        return rows
    
    def _find_aligned_groups_in_row(self, row_regions: List[Dict], align_type: str) -> List[List[Dict]]:
        """在单行内查找对齐组"""
        if len(row_regions) < 2:
            return []
        
        # 对于行内对齐，我们需要更宽松的判断
        # 因为同一行的文字可能是左右分布，而不是严格对齐
        
        if align_type == 'left':
            # 检查是否有多个区域的左边界接近
            return self._find_aligned_groups(row_regions, 'left')
        elif align_type == 'right':
            # 检查是否有多个区域的右边界接近
            return self._find_aligned_groups(row_regions, 'right')
        elif align_type == 'center':
            # 检查是否有多个区域的中心接近
            return self._find_aligned_groups(row_regions, 'center')
        else:
            return []
    
    def _find_cross_row_aligned_groups(self, regions: List[Dict], align_type: str) -> List[List[Dict]]:
        """查找跨行的对齐组（原有逻辑）"""
        return self._find_aligned_groups(regions, align_type)
    
    def _find_aligned_groups(self, regions: List[Dict], align_type: str) -> List[List[Dict]]:
        """查找对齐组"""
        if align_type == 'left':
            key_func = lambda r: r['left']
        elif align_type == 'right':
            key_func = lambda r: r['right']
        else:  # center
            key_func = lambda r: r['center'][0]
        
        # 按对齐位置分组
        position_groups = defaultdict(list)
        for region in regions:
            pos = key_func(region)
            position_groups[pos].append(region)
        
        # 合并相近位置的组
        aligned_groups = []
        sorted_positions = sorted(position_groups.keys())
        
        current_group = []
        for pos in sorted_positions:
            if not current_group:
                current_group.extend(position_groups[pos])
            else:
                # 检查是否与当前组的位置接近
                last_pos = key_func(current_group[-1])
                if abs(pos - last_pos) <= self.alignment_threshold:
                    current_group.extend(position_groups[pos])
                else:
                    if len(current_group) >= 2:
                        aligned_groups.append(current_group)
                    current_group = list(position_groups[pos])
        
        # 添加最后一组
        if len(current_group) >= 2:
            aligned_groups.append(current_group)
        
        return aligned_groups
    
    def _determine_primary_alignment(self, left_groups, center_groups, right_groups) -> str:
        """确定主要对齐模式"""
        left_count = sum(len(group) for group in left_groups)
        center_count = sum(len(group) for group in center_groups)
        right_count = sum(len(group) for group in right_groups)
        
        if left_count > center_count and left_count > right_count:
            return 'left'
        elif right_count > center_count and right_count > left_count:
            return 'right'
        elif center_count > 0:
            return 'center'
        else:
            return 'mixed'
    
    def _analyze_vertical_distribution(self, regions: List[Dict]) -> Dict[str, Any]:
        """分析垂直分布模式"""
        if len(regions) < 2:
            return {'type': 'single', 'rows': 1, 'columns': 1}
        
        # 按Y坐标分组找行
        y_positions = [r['top'] for r in regions]
        y_groups = self._group_by_proximity(y_positions, self.proximity_threshold)
        rows = len(y_groups)
        
        # 按X坐标分组找列
        x_positions = [r['left'] for r in regions]
        x_groups = self._group_by_proximity(x_positions, self.proximity_threshold)
        columns = len(x_groups)
        
        # 确定分布类型
        if rows == 1 and columns > 1:
            dist_type = 'horizontal'
        elif rows > 1 and columns == 1:
            dist_type = 'vertical'
        elif rows == 2 and columns == 2:
            dist_type = 'grid_2x2'
        elif rows > 2 or columns > 2:
            dist_type = 'complex'
        else:
            dist_type = 'scattered'
        
        return {
            'type': dist_type,
            'rows': rows,
            'columns': columns,
            'row_groups': y_groups,
            'column_groups': x_groups
        }
    
    def _group_by_proximity(self, positions: List[int], threshold: int) -> List[List[int]]:
        """按邻近度分组"""
        if not positions:
            return []
        
        sorted_positions = sorted(set(positions))
        groups = []
        current_group = [sorted_positions[0]]
        
        for i in range(1, len(sorted_positions)):
            if sorted_positions[i] - sorted_positions[i-1] <= threshold:
                current_group.append(sorted_positions[i])
            else:
                groups.append(current_group)
                current_group = [sorted_positions[i]]
        
        groups.append(current_group)
        return groups
    
    def _analyze_spacing_pattern(self, regions: List[Dict]) -> Dict[str, Any]:
        """分析间距模式"""
        if len(regions) < 2:
            return {'type': 'none'}
        
        # 计算水平间距
        h_spacings = []
        for i in range(len(regions)):
            for j in range(i + 1, len(regions)):
                r1, r2 = regions[i], regions[j]
                # 只计算同一行的间距
                if abs(r1['top'] - r2['top']) <= self.alignment_threshold:
                    spacing = abs(r1['center'][0] - r2['center'][0])
                    h_spacings.append(spacing)
        
        # 计算垂直间距
        v_spacings = []
        for i in range(len(regions)):
            for j in range(i + 1, len(regions)):
                r1, r2 = regions[i], regions[j]
                # 只计算同一列的间距
                if abs(r1['center'][0] - r2['center'][0]) <= self.alignment_threshold:
                    spacing = abs(r1['center'][1] - r2['center'][1])
                    v_spacings.append(spacing)
        
        # 分析间距规律性
        h_regularity = self._analyze_spacing_regularity(h_spacings)
        v_regularity = self._analyze_spacing_regularity(v_spacings)
        
        return {
            'type': 'regular' if h_regularity == 'regular' and v_regularity == 'regular' else 'irregular',
            'horizontal_regularity': h_regularity,
            'vertical_regularity': v_regularity,
            'horizontal_spacings': h_spacings,
            'vertical_spacings': v_spacings
        }
    
    def _analyze_spacing_regularity(self, spacings: List[int]) -> str:
        """分析间距规律性"""
        if len(spacings) < 2:
            return 'insufficient_data'
        
        # 计算变异系数
        mean_spacing = np.mean(spacings)
        std_spacing = np.std(spacings)
        
        if mean_spacing == 0:
            return 'zero_spacing'
        
        cv = std_spacing / mean_spacing
        
        if cv < 0.2:
            return 'regular'
        elif cv < 0.5:
            return 'semi_regular'
        else:
            return 'irregular'

    def _identify_layout_mode(
        self,
        regions: List[Dict],
        h_align: Dict,
        v_dist: Dict,
        spacing: Dict
    ) -> str:
        """识别整体布局模式"""
        num_regions = len(regions)

        if num_regions == 1:
            return 'single_text'
        elif num_regions == 2:
            return 'dual_text'

        # 基于垂直分布判断
        if v_dist['type'] == 'horizontal':
            if h_align['type'] == 'left':
                return 'horizontal_left_aligned'
            elif h_align['type'] == 'center':
                return 'horizontal_center_aligned'
            elif h_align['type'] == 'right':
                return 'horizontal_right_aligned'
            else:
                return 'horizontal_mixed'
        elif v_dist['type'] == 'vertical':
            if h_align['type'] == 'left':
                return 'vertical_left_aligned'
            elif h_align['type'] == 'center':
                return 'vertical_center_aligned'
            else:
                return 'vertical_mixed'
        elif v_dist['type'] == 'grid_2x2':
            return 'grid_2x2'
        elif v_dist['type'] == 'complex':
            return 'complex_layout'
        else:
            return 'scattered_layout'

    def _generate_alignment_strategies(
        self,
        layout_mode: str,
        h_align: Dict,
        v_dist: Dict
    ) -> List[Dict[str, Any]]:
        """生成对齐策略建议"""
        strategies = []

        if layout_mode == 'horizontal_left_aligned':
            strategies.append({
                'type': 'preserve_left_alignment',
                'description': '保持左对齐布局',
                'regions': 'all',
                'priority': 'high'
            })
        elif layout_mode == 'horizontal_center_aligned':
            strategies.append({
                'type': 'preserve_center_alignment',
                'description': '保持居中对齐布局',
                'regions': 'all',
                'priority': 'high'
            })
        elif layout_mode == 'vertical_left_aligned':
            strategies.append({
                'type': 'preserve_vertical_left_alignment',
                'description': '保持垂直左对齐布局',
                'regions': 'all',
                'priority': 'high'
            })
        elif layout_mode == 'grid_2x2':
            strategies.append({
                'type': 'preserve_grid_layout',
                'description': '保持2x2网格布局',
                'regions': 'all',
                'priority': 'high'
            })
        elif layout_mode == 'complex_layout':
            # 复杂布局，提供多种策略
            if len(h_align['left_groups']) > 0:
                strategies.append({
                    'type': 'group_left_alignment',
                    'description': '对左对齐组保持左对齐',
                    'regions': 'left_aligned_groups',
                    'priority': 'medium'
                })
            if len(h_align['center_groups']) > 0:
                strategies.append({
                    'type': 'group_center_alignment',
                    'description': '对居中组保持居中对齐',
                    'regions': 'center_aligned_groups',
                    'priority': 'medium'
                })
            if len(h_align['right_groups']) > 0:
                strategies.append({
                    'type': 'group_right_alignment',
                    'description': '对右对齐组保持右对齐',
                    'regions': 'right_aligned_groups',
                    'priority': 'medium'
                })
            if len(h_align.get('distribution_groups', [])) > 0:
                strategies.append({
                    'type': 'preserve_distribution',
                    'description': '保持分布组的相对位置',
                    'regions': 'distribution_groups',
                    'priority': 'high'
                })
        else:
            # 默认策略
            strategies.append({
                'type': 'center_alignment',
                'description': '使用居中对齐（默认策略）',
                'regions': 'all',
                'priority': 'low'
            })

        return strategies

    def _create_simple_layout_result(self, regions: List[TextRegion]) -> LayoutResult:
        """创建简单布局结果"""
        regions_data = self._convert_regions_to_dict(regions)

        return LayoutResult(
            layout_mode='single_text' if len(regions) <= 1 else 'simple',
            regions=regions_data,
            horizontal_alignment={'type': 'single', 'left_groups': [], 'center_groups': [], 'right_groups': [], 'distribution_groups': []},
            vertical_distribution={'type': 'single', 'rows': 1, 'columns': 1},
            alignment_strategies=[{
                'type': 'center_alignment',
                'description': '使用居中对齐',
                'regions': 'all',
                'priority': 'default'
            }]
        )

    def _print_layout_analysis_result(self, result: LayoutResult):
        """打印布局分析结果"""
        print(f"\n布局分析结果:")
        print(f"  文字区域数量: {len(result.regions)}")
        print(f"  布局模式: {result.layout_mode}")
        print(f"  水平对齐: {result.horizontal_alignment['type']}")
        print(f"  垂直分布: {result.vertical_distribution['type']} "
              f"({result.vertical_distribution['rows']}行 × {result.vertical_distribution['columns']}列)")

        # 打印详细的分组情况
        h_align = result.horizontal_alignment
        if h_align['type'] != 'single':
            print(f"  对齐组信息:")

            # 左对齐组详情
            if h_align['left_groups']:
                print(f"    左对齐组: {len(h_align['left_groups'])}个")
                for i, group in enumerate(h_align['left_groups']):
                    texts = [region['text'] for region in group]
                    left_pos = group[0]['left']
                    print(f"      组{i+1}: {texts} (左边界: {left_pos}px)")

            # 居中对齐组详情
            if h_align['center_groups']:
                print(f"    居中对齐组: {len(h_align['center_groups'])}个")
                for i, group in enumerate(h_align['center_groups']):
                    texts = [region['text'] for region in group]
                    center_pos = group[0]['center'][0]
                    print(f"      组{i+1}: {texts} (中心位置: {center_pos}px)")

            # 右对齐组详情
            if h_align['right_groups']:
                print(f"    右对齐组: {len(h_align['right_groups'])}个")
                for i, group in enumerate(h_align['right_groups']):
                    texts = [region['text'] for region in group]
                    right_pos = group[0]['right']
                    print(f"      组{i+1}: {texts} (右边界: {right_pos}px)")

            # 分布组详情
            if h_align.get('distribution_groups'):
                print(f"    分布组: {len(h_align['distribution_groups'])}个")
                for i, group in enumerate(h_align['distribution_groups']):
                    texts = [region['text'] for region in group]
                    y_pos = group[0]['top']
                    print(f"      组{i+1}: {texts} (行位置: Y={y_pos}px)")

        # 打印垂直分布详情
        v_dist = result.vertical_distribution
        if v_dist['type'] != 'single':
            print(f"  垂直分布详情:")
            if 'row_groups' in v_dist and v_dist['row_groups']:
                print(f"    行分组: {len(v_dist['row_groups'])}行")
                for i, row_positions in enumerate(v_dist['row_groups']):
                    avg_y = sum(row_positions) // len(row_positions)
                    print(f"      第{i+1}行: Y坐标范围 {min(row_positions)}-{max(row_positions)}px (平均: {avg_y}px)")

            if 'column_groups' in v_dist and v_dist['column_groups']:
                print(f"    列分组: {len(v_dist['column_groups'])}列")
                for i, col_positions in enumerate(v_dist['column_groups']):
                    avg_x = sum(col_positions) // len(col_positions)
                    print(f"      第{i+1}列: X坐标范围 {min(col_positions)}-{max(col_positions)}px (平均: {avg_x}px)")

        # 打印文字区域排布
        print(f"  文字区域排布:")
        sorted_regions = sorted(result.regions, key=lambda r: (r['top'], r['left']))
        for i, region in enumerate(sorted_regions):
            x, y, w, h = region['bbox']
            center_x, center_y = region['center']
            print(f"    {i+1}. '{region['text']}' at ({x}, {y}) 尺寸({w}×{h}) 中心({center_x}, {center_y})")

        # 打印策略建议
        if result.alignment_strategies:
            print(f"  推荐策略:")
            for strategy in result.alignment_strategies:
                print(f"    - {strategy['description']} (优先级: {strategy['priority']})")

    def get_alignment_for_region(self, region_bbox: Tuple[int, int, int, int], layout_result: LayoutResult) -> str:
        """
        为特定区域获取对齐方式

        Args:
            region_bbox: 区域边界框 (x, y, w, h)
            layout_result: 布局分析结果

        Returns:
            str: 对齐方式 ('left', 'center', 'right')
        """
        x, y, w, _ = region_bbox
        center_x = x + w // 2

        try:
            h_align = layout_result.horizontal_alignment

            # 检查左对齐组
            for group in h_align.get('left_groups', []):
                for region in group:
                    if (abs(region.get('left', 0) - x) <= self.alignment_threshold and
                        abs(region.get('top', 0) - y) <= self.alignment_threshold):
                        return 'left'

            # 检查右对齐组
            for group in h_align.get('right_groups', []):
                for region in group:
                    if (abs(region.get('right', 0) - (x + w)) <= self.alignment_threshold and
                        abs(region.get('top', 0) - y) <= self.alignment_threshold):
                        return 'right'

            # 检查居中对齐组
            for group in h_align.get('center_groups', []):
                for region in group:
                    region_center_x = region.get('center', [0, 0])[0]
                    if abs(region_center_x - center_x) <= self.alignment_threshold:
                        return 'center'

            # 检查分布组
            for group in h_align.get('distribution_groups', []):
                for region in group:
                    if (abs(region.get('left', 0) - x) <= self.alignment_threshold and
                        abs(region.get('top', 0) - y) <= self.alignment_threshold):
                        # 对于分布组，根据在组内的相对位置确定对齐方式
                        return self._determine_alignment_in_distribution_group(region, group)

            # 默认居中对齐
            return 'center'

        except Exception as e:
            print(f"获取区域对齐方式失败: {e}")
            return 'center'
    
    def _determine_alignment_in_distribution_group(self, target_region: Dict, group: List[Dict]) -> str:
        """在分布组内确定区域的对齐方式"""
        if len(group) < 2:
            return 'center'
        
        # 按X坐标排序
        sorted_group = sorted(group, key=lambda r: r['center'][0])
        
        # 找到目标区域在组内的位置
        target_center_x = target_region['center'][0]
        
        # 如果是最左边的区域，使用左对齐
        if target_center_x == sorted_group[0]['center'][0]:
            return 'left'
        # 如果是最右边的区域，使用右对齐
        elif target_center_x == sorted_group[-1]['center'][0]:
            return 'right'
        # 中间的区域使用居中对齐
        else:
            return 'center'

    def _generate_debug_outputs(self, chinese_regions: List[TextRegion], layout_result: LayoutResult):
        """生成调试图像和数据文件"""
        try:
            # 确保调试目录存在
            debug_dir = self.config_manager.config.layout_debug_dir
            os.makedirs(debug_dir, exist_ok=True)
            
            # 使用原始图像作为基础（从第一个区域推断原图路径）
            base_image = self._load_original_image(chinese_regions)
            
            # 生成调试图像
            self._save_alignment_groups_debug(base_image, layout_result, debug_dir)
            self._save_distribution_grid_debug(base_image, layout_result, debug_dir)
            
            # 保存JSON数据
            self._save_layout_data_json(layout_result, debug_dir)
            
            print(f"Layout调试文件已保存到: {debug_dir}")
            
        except Exception as e:
            print(f"生成Layout调试文件失败: {e}")
    
    def _load_original_image(self, regions: List[TextRegion]) -> np.ndarray:
        """加载原始图像"""
        try:
            # 尝试加载原始图像（假设在处理example.jpg）
            # 这里可以通过全局变量或配置获取当前处理的图像路径
            # 暂时使用硬编码，后续可以优化
            image_path = "example.jpg"  # 可以通过参数传递
            if os.path.exists(image_path):
                return cv2.imread(image_path)
        except:
            pass
        
        # 如果无法加载原图，创建基础图像
        return self._create_base_image(regions)
    
    def _create_base_image(self, regions: List[TextRegion]) -> np.ndarray:
        """创建基础图像用于调试绘制"""
        if not regions:
            return np.ones((600, 800, 3), dtype=np.uint8) * 255  # 白色背景
        
        # 计算所有区域的边界
        all_x = []
        all_y = []
        for region in regions:
            x, y, w, h = region.bbox
            all_x.extend([x, x + w])
            all_y.extend([y, y + h])
        
        min_x, max_x = min(all_x), max(all_x)
        min_y, max_y = min(all_y), max(all_y)
        
        # 添加边距
        margin = 50
        width = max_x - min_x + 2 * margin
        height = max_y - min_y + 2 * margin
        
        # 创建白色背景图像
        base_image = np.ones((height, width, 3), dtype=np.uint8) * 255
        
        return base_image
    
    def _save_alignment_groups_debug(self, base_image: np.ndarray, layout_result: LayoutResult, debug_dir: str):
        """保存对齐组调试图像"""
        try:
            # 转换为PIL图像以支持中文
            pil_image = Image.fromarray(cv2.cvtColor(base_image, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_image)
            
            # 尝试加载中文字体
            font = self._load_chinese_font()
            
            # 定义颜色
            colors = {
                'left_aligned': (0, 255, 0),      # 绿色 - 左对齐组
                'center_aligned': (0, 0, 255),    # 蓝色 - 居中对齐组  
                'right_aligned': (255, 0, 0),     # 红色 - 右对齐组
                'distribution': (255, 0, 255),    # 紫色 - 分布组
                'ungrouped': (255, 165, 0),       # 橙色 - 未分组区域
                'alignment_line': (255, 255, 0)   # 黄色 - 对齐基准线
            }
            
            # 收集所有已分组的区域ID
            grouped_region_ids = set()
            
            # 获取对齐组信息
            h_align = layout_result.horizontal_alignment
            left_groups = h_align.get('left_groups', [])
            center_groups = h_align.get('center_groups', [])
            right_groups = h_align.get('right_groups', [])
            
            # 绘制左对齐组
            for group_idx, group in enumerate(left_groups):
                group_color = colors['left_aligned']
                # 计算对齐基准线（最左边的位置）
                min_left = min(region['left'] for region in group)
                
                for region in group:
                    grouped_region_ids.add(region['id'])
                    # 绘制区域边框
                    self._draw_region_box(draw, region, group_color, f"L{group_idx}")
                
                # 绘制对齐基准线
                if group:
                    top_y = min(region['top'] for region in group) - 10
                    bottom_y = max(region['bottom'] for region in group) + 10
                    draw.line([(min_left, top_y), (min_left, bottom_y)], 
                             fill=colors['alignment_line'], width=2)
            
            # 绘制居中对齐组
            for group_idx, group in enumerate(center_groups):
                group_color = colors['center_aligned']
                # 计算对齐基准线（中心位置）
                center_x = sum(region['center'][0] for region in group) / len(group)
                
                for region in group:
                    grouped_region_ids.add(region['id'])
                    # 绘制区域边框
                    self._draw_region_box(draw, region, group_color, f"C{group_idx}")
                
                # 绘制对齐基准线
                if group:
                    top_y = min(region['top'] for region in group) - 10
                    bottom_y = max(region['bottom'] for region in group) + 10
                    draw.line([(center_x, top_y), (center_x, bottom_y)], 
                             fill=colors['alignment_line'], width=2)
            
            # 绘制右对齐组
            for group_idx, group in enumerate(right_groups):
                group_color = colors['right_aligned']
                # 计算对齐基准线（最右边的位置）
                max_right = max(region['right'] for region in group)
                
                for region in group:
                    grouped_region_ids.add(region['id'])
                    # 绘制区域边框
                    self._draw_region_box(draw, region, group_color, f"R{group_idx}")
                
                # 绘制对齐基准线
                if group:
                    top_y = min(region['top'] for region in group) - 10
                    bottom_y = max(region['bottom'] for region in group) + 10
                    draw.line([(max_right, top_y), (max_right, bottom_y)], 
                             fill=colors['alignment_line'], width=2)
            
            # 绘制分布组
            distribution_groups = h_align.get('distribution_groups', [])
            for group_idx, group in enumerate(distribution_groups):
                group_color = colors['distribution']
                
                for region in group:
                    grouped_region_ids.add(region['id'])
                    # 绘制区域边框
                    self._draw_region_box(draw, region, group_color, f"D{group_idx}")
                
                # 绘制分布组的连接线（水平线连接同一行的区域）
                if len(group) >= 2:
                    # 计算行的Y坐标
                    avg_y = sum(region['center'][1] for region in group) // len(group)
                    min_x = min(region['left'] for region in group)
                    max_x = max(region['right'] for region in group)
                    
                    # 绘制水平连接线
                    draw.line([(min_x, avg_y), (max_x, avg_y)], 
                             fill=colors['distribution'], width=2)
            
            # 绘制未分组的区域
            all_regions = []
            for group in left_groups:
                all_regions.extend(group)
            for group in center_groups:
                all_regions.extend(group)
            for group in right_groups:
                all_regions.extend(group)
            
            # 找出未分组的区域（从layout_result.regions中找出未被分组的）
            all_region_ids = {region['id'] for region in layout_result.regions}
            ungrouped_region_ids = all_region_ids - grouped_region_ids
            ungrouped_regions = [region for region in layout_result.regions if region['id'] in ungrouped_region_ids]
            
            for region in ungrouped_regions:
                # 绘制未分组区域边框
                self._draw_region_box(draw, region, colors['ungrouped'], "U")
            
            # 添加图例
            self._draw_legend(draw, font, colors, len(left_groups), 
                            len(center_groups), len(right_groups), 
                            len(distribution_groups), len(ungrouped_regions))
            
            # 转换回OpenCV格式并保存
            cv2_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            cv2.imwrite(os.path.join(debug_dir, "alignment_groups.png"), cv2_image)
            
        except Exception as e:
            print(f"保存对齐组调试图像失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _draw_region_box(self, draw: ImageDraw.Draw, region: Dict, color: tuple, label: str):
        """绘制区域边框和标签"""
        # 绘制边框
        draw.rectangle([region['left'], region['top'], region['right'], region['bottom']], 
                      outline=color, width=2)
        
        # 绘制标签背景
        font = self._load_chinese_font()
        label_text = f"{label}: {region['text']}"
        
        # 计算文字尺寸
        bbox = draw.textbbox((0, 0), label_text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # 绘制标签背景
        label_x = region['left']
        label_y = region['top'] - text_height - 5
        if label_y < 0:
            label_y = region['bottom'] + 5
        
        draw.rectangle([label_x, label_y, label_x + text_width + 4, label_y + text_height + 4], 
                      fill=color)
        
        # 绘制标签文字
        draw.text((label_x + 2, label_y + 2), label_text, fill=(255, 255, 255), font=font)
    
    def _draw_legend(self, draw: ImageDraw.Draw, font, colors: dict, 
                    left_count: int, center_count: int, right_count: int, distribution_count: int, ungrouped_count: int):
        """绘制图例"""
        legend_items = [
            (f"左对齐组 ({left_count})", colors['left_aligned']),
            (f"居中对齐组 ({center_count})", colors['center_aligned']),
            (f"右对齐组 ({right_count})", colors['right_aligned']),
            (f"分布组 ({distribution_count})", colors['distribution']),
            (f"未分组区域 ({ungrouped_count})", colors['ungrouped']),
            ("对齐基准线", colors['alignment_line'])
        ]
        
        # 在图像右上角绘制图例
        legend_x = 10
        legend_y = 10
        
        for i, (text, color) in enumerate(legend_items):
            y_pos = legend_y + i * 25
            
            # 绘制色块
            draw.rectangle([legend_x, y_pos, legend_x + 15, y_pos + 15], fill=color)
            
            # 绘制文字
            draw.text((legend_x + 20, y_pos), text, fill=(255, 255, 255), font=font)
    
    def _save_distribution_grid_debug(self, base_image: np.ndarray, layout_result: LayoutResult, debug_dir: str):
        """保存分布网格调试图像"""
        try:
            # 转换为PIL图像以支持中文
            pil_image = Image.fromarray(cv2.cvtColor(base_image, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_image)
            
            # 加载中文字体
            font = self._load_chinese_font()
            
            # 绘制分布网格
            v_dist = layout_result.vertical_distribution
            image_size = (base_image.shape[1], base_image.shape[0])  # (width, height)
            
            # 绘制网格线和区域
            self._draw_distribution_grid(draw, v_dist, image_size)
            
            # 绘制所有文字区域
            for region in layout_result.regions:
                # 绘制区域边框（浅蓝色）
                draw.rectangle([region['left'], region['top'], region['right'], region['bottom']], 
                              outline=(173, 216, 230), width=1)
                
                # 绘制文字标签
                label_text = region['text']
                try:
                    # 计算文字位置
                    bbox = draw.textbbox((0, 0), label_text, font=font)
                    text_height = bbox[3] - bbox[1]
                    
                    # 绘制半透明背景
                    label_x = region['left']
                    label_y = region['top'] - text_height - 2
                    if label_y < 0:
                        label_y = region['bottom'] + 2
                    
                    # 绘制文字
                    draw.text((label_x, label_y), label_text, fill=(255, 255, 255), font=font)
                except:
                    # 如果字体加载失败，使用默认字体
                    draw.text((region['left'], region['top'] - 20), label_text, fill=(255, 255, 255))
            
            # 添加网格信息
            self._add_grid_info(draw, v_dist, font, image_size)
            
            # 转换回OpenCV格式并保存
            final_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            output_path = os.path.join(debug_dir, "distribution_grid.png")
            cv2.imwrite(output_path, final_image)
            
        except Exception as e:
            print(f"保存分布网格调试图像失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _draw_distribution_grid(self, draw: ImageDraw.Draw, v_dist: Dict, image_size: Tuple[int, int]):
        """绘制分布网格"""
        width, height = image_size
        
        # 绘制行线
        for row_group in v_dist.get('row_groups', []):
            if row_group:
                y = sum(row_group) // len(row_group)  # 平均位置
                # 绘制水平网格线（浅灰色虚线）
                for x in range(0, width, 20):
                    draw.line([(x, y), (x + 10, y)], fill=(200, 200, 200), width=1)
        
        # 绘制列线
        for col_group in v_dist.get('column_groups', []):
            if col_group:
                x = sum(col_group) // len(col_group)  # 平均位置
                # 绘制垂直网格线（浅灰色虚线）
                for y in range(0, height, 20):
                    draw.line([(x, y), (x, y + 10)], fill=(200, 200, 200), width=1)
    
    def _add_grid_info(self, draw: ImageDraw.Draw, v_dist: Dict, font, image_size: Tuple[int, int]):
        """添加网格信息"""
        width, _ = image_size
        info_text = f"网格: {v_dist['rows']}行 × {v_dist['columns']}列"
        info_text2 = f"分布: {v_dist['type']}"
        
        try:
            draw.text((width - 200, 10), info_text, fill=(0, 0, 0), font=font)
            draw.text((width - 200, 35), info_text2, fill=(0, 0, 0), font=font)
        except:
            draw.text((width - 200, 10), info_text, fill=(0, 0, 0))
            draw.text((width - 200, 35), info_text2, fill=(0, 0, 0))
    
    def _load_chinese_font(self):
        """加载中文字体"""
        try:
            # 尝试使用系统中文字体
            font = ImageFont.truetype("/System/Library/Fonts/PingFang.ttc", 20)
        except:
            try:
                # 尝试使用项目中的字体
                font_path = os.path.join(self.config_manager.get_fonts_dir(), "思源黑体", "SourceHanSans-VF.otf")
                font = ImageFont.truetype(font_path, 20)
            except:
                # 使用默认字体
                font = ImageFont.load_default()
        return font
    
    def _save_layout_data_json(self, layout_result: LayoutResult, debug_dir: str):
        """保存布局数据为JSON格式"""
        
        # 序列化布局结果
        layout_data = {
            "metadata": {
                "timestamp": self._get_timestamp(),
                "total_regions": len(layout_result.regions),
                "layout_mode": layout_result.layout_mode,
                "alignment_threshold": self.alignment_threshold,
                "proximity_threshold": self.proximity_threshold
            },
            "horizontal_alignment": {
                "type": layout_result.horizontal_alignment.get('type', 'unknown'),     # 主要对齐类型
                "left_groups": [                                                       # 左对齐组
                    [
                        {
                            "id": region.get('id', 0),                                # 区域ID
                            "text": region.get('text', ''),                           # 文字内容
                            "bbox": region.get('bbox', [0, 0, 0, 0]),                 # 边界框 [x, y, w, h]
                            "left": region.get('left', 0),                            # 左边界坐标
                            "right": region.get('right', 0),                          # 右边界坐标
                            "top": region.get('top', 0),                              # 上边界坐标
                            "bottom": region.get('bottom', 0),                        # 下边界坐标
                            "center": region.get('center', [0, 0])                    # 中心点坐标 [x, y]
                        }
                        for region in group
                    ]
                    for group in layout_result.horizontal_alignment.get('left_groups', [])
                ],
                "center_groups": [                                                     # 居中对齐组（同样结构）
                    [
                        {
                            "id": region.get('id', 0),
                            "text": region.get('text', ''),
                            "bbox": region.get('bbox', [0, 0, 0, 0]),
                            "left": region.get('left', 0),
                            "right": region.get('right', 0),
                            "top": region.get('top', 0),
                            "bottom": region.get('bottom', 0),
                            "center": region.get('center', [0, 0])
                        }
                        for region in group
                    ]
                    for group in layout_result.horizontal_alignment.get('center_groups', [])
                ],
                "right_groups": [                                                      # 右对齐组（同样结构）
                    [
                        {
                            "id": region.get('id', 0),
                            "text": region.get('text', ''),
                            "bbox": region.get('bbox', [0, 0, 0, 0]),
                            "left": region.get('left', 0),
                            "right": region.get('right', 0),
                            "top": region.get('top', 0),
                            "bottom": region.get('bottom', 0),
                            "center": region.get('center', [0, 0])
                        }
                        for region in group
                    ]
                    for group in layout_result.horizontal_alignment.get('right_groups', [])
                ],
                "distribution_groups": [                                               # 分布组
                    [
                        {
                            "id": region.get('id', 0),
                            "text": region.get('text', ''),
                            "bbox": region.get('bbox', [0, 0, 0, 0]),
                            "left": region.get('left', 0),
                            "right": region.get('right', 0),
                            "top": region.get('top', 0),
                            "bottom": region.get('bottom', 0),
                            "center": region.get('center', [0, 0])
                        }
                        for region in group
                    ]
                    for group in layout_result.horizontal_alignment.get('distribution_groups', [])
                ]
            },
            "vertical_distribution": {
                "type": layout_result.vertical_distribution.get('type', 'unknown'),   # 分布类型
                "rows": layout_result.vertical_distribution.get('rows', 0),           # 检测到的行数
                "columns": layout_result.vertical_distribution.get('columns', 0),     # 检测到的列数
                "row_groups": layout_result.vertical_distribution.get('row_groups', []),      # 行位置分组
                "column_groups": layout_result.vertical_distribution.get('column_groups', []) # 列位置分组
            },
            "alignment_strategies": [
                {
                    "type": strategy.get('type', ''),                                 # 策略类型
                    "description": strategy.get('description', ''),                   # 策略描述
                    "regions": strategy.get('regions', ''),                           # 适用区域
                    "priority": strategy.get('priority', '')                          # 优先级
                }
                for strategy in layout_result.alignment_strategies
            ],
            "regions_details": [
                {
                    "id": region.get('id', 0),                                        # 区域ID
                    "text": region.get('text', ''),                                   # 文字内容
                    "bbox": region.get('bbox', [0, 0, 0, 0]),                         # 边界框 [x, y, w, h]
                    "center": region.get('center', [0, 0]),                           # 中心点坐标 [x, y]
                    "left": region.get('left', 0),                                    # 左边界坐标
                    "right": region.get('right', 0),                                  # 右边界坐标
                    "top": region.get('top', 0),                                      # 上边界坐标
                    "bottom": region.get('bottom', 0)                                 # 下边界坐标
                }
                for region in layout_result.regions
            ]
        }
        
        # 保存JSON文件
        output_path = os.path.join(debug_dir, "layout_data.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(layout_data, f, ensure_ascii=False, indent=2)
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

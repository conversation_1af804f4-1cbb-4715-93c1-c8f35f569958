import os
import sys
# 确保可以导入项目根目录下的 core 包
ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
if ROOT_DIR not in sys.path:
    sys.path.insert(0, ROOT_DIR)

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from paddleocr import PaddleOCR

from core.translator import SimpleTranslator


def measure_and_debug(text: str, font_path: str, font_size: int, output_prefix: str):
    """测量文本渲染高度并保存调试图像
    参数:
        text: 待测文字
        font_path: 字体文件路径
        font_size: 字号
        output_prefix: 输出文件前缀(不含扩展名)
    生成的文件:
        <prefix>_drawn.png   - 渲染原图
        <prefix>_binary.png  - 二值化图
        <prefix>_bbox.png    - 标注边框图
    返回:
        渲染高度(像素)
    """
    os.makedirs("test_output", exist_ok=True)

    sample_width, sample_height = 300, 150

    # 1. 创建测试画布并绘制文字
    test_image = Image.new('RGB', (sample_width, sample_height), color=(255, 255, 255))
    draw = ImageDraw.Draw(test_image)
    font = ImageFont.truetype(font_path, font_size)

    text_x = sample_width // 4
    text_y = sample_height // 4
    draw.text((text_x, text_y), text, font=font, fill=(0, 0, 0))

    test_image_path = os.path.join("test_output", f"{output_prefix}_drawn.png")
    test_image.save(test_image_path)

    # 2. 转为灰度并二值化
    gray = cv2.cvtColor(np.array(test_image), cv2.COLOR_RGB2GRAY)
    _, binary = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY_INV)
    binary_path = os.path.join("test_output", f"{output_prefix}_binary.png")
    cv2.imwrite(binary_path, binary)

    # 3. 查找轮廓并测量高度
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    height_px = None
    bbox_img = np.array(test_image).copy()

    if contours:
        largest = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(largest)
        height_px = h
        # 绘制边框
        cv2.rectangle(bbox_img, (x, y), (x + w, y + h), (255, 0, 0), 2)

    bbox_path = os.path.join("test_output", f"{output_prefix}_bbox.png")
    cv2.imwrite(bbox_path, cv2.cvtColor(bbox_img, cv2.COLOR_RGB2BGR))

    return height_px


def main():
    translator = SimpleTranslator()

    # 选择示例文本和字体
    text_samples = [
        ("久睡不塌", 64),   # 目标高度64
        ("适用更久", 60)    # 目标高度60
    ]

    # 使用项目内台北黑体粗体字体
    font_path = os.path.join("fonts", "台北黑体", "TaipeiSans-Bold.ttf")
    assert os.path.exists(font_path), f"Font not found: {font_path}"

    IMG_PATH = os.path.join(ROOT_DIR, 'example.jpg')

    ocr = PaddleOCR(use_angle_cls=False, lang='ch')
    # 兼容新版 paddleocr，使用 predict
    ocr_res_list = ocr.predict(input=IMG_PATH)

    # predict 返回 list，每个元素包含 'dt_polys', 'rec_texts', 'rec_scores'
    detected_map = {}
    for res in ocr_res_list:
        dt_polys = res.get('dt_polys', [])
        rec_texts = res.get('rec_texts', [])
        for poly, txt in zip(dt_polys, rec_texts):
            pts = np.array(poly, dtype=np.int32)
            x, y, w, h = cv2.boundingRect(pts)
            detected_map[txt] = (x, y, w, h)

    print("OCR检测文本:")
    for t in detected_map.keys():
        print("  ", t)

    for text, _ in text_samples:
        if text not in detected_map:
            print(f"警告: 未在OCR结果中找到 {text}")
            continue

        x, y, w, h = detected_map[text]
        orig_img = cv2.imread(IMG_PATH)
        roi = orig_img[y:y+h, x:x+w].copy()
        real_h = translator.measure_roi_text_height(roi, prefix=f'roi_{text}')
        print(f"OCR测得ROI高度 {h}px  →  真实笔画高度 {real_h}px")

        init_size = real_h
        measured = translator.measure_rendered_text_height(text, font_path, init_size)
        print(f"文本 '{text}' 用字号{init_size}px 渲染高度测得 {measured}px (目标{real_h}px)")

        prefix = f"{text}_size{init_size}"
        measure_and_debug(text, font_path, init_size, prefix)

        matched_size = translator.calculate_font_size_by_height_matching(
            text, real_h, font_path, initial_guess=init_size)
        print(f"  ↳ 高度匹配算法推荐字号: {matched_size}px")
        prefix2 = f"{text}_matched{matched_size}"
        measure_and_debug(text, font_path, matched_size, prefix2)


if __name__ == "__main__":
    main() 
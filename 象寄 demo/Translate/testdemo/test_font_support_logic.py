#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字体日文支持检测逻辑的修复
"""

import sys
import os
sys.path.append('..')

from core.font_matcher import SimpleFontMatcher
from core.translator import SimpleTranslator

def test_font_support_detection():
    """测试字体支持检测逻辑"""
    print("=== 测试字体日文支持检测逻辑 ===\n")
    
    # 初始化
    font_matcher = SimpleFontMatcher()
    translator = SimpleTranslator()
    
    print("1. 字体匹配器中的可用字体:")
    for font_name, font_path in font_matcher.fonts.items():
        print(f"   {font_name}: {font_path}")
    
    print("\n2. 测试各字体的日文支持:")
    for font_name, font_path in font_matcher.fonts.items():
        if font_path and os.path.exists(font_path):
            supports_japanese = translator.test_japanese_support(font_path)
            status = "✅ 支持" if supports_japanese else "❌ 不支持"
            print(f"   {font_name}: {status}")
        else:
            print(f"   {font_name}: ⚠️ 路径不存在 ({font_path})")
    
    print("\n3. 特别测试 Spoqa Han Sans Bold:")
    spoqa_font = "Spoqa Han Sans Bold"
    if spoqa_font in font_matcher.fonts:
        font_path = font_matcher.fonts[spoqa_font]
        print(f"   路径: {font_path}")
        print(f"   文件存在: {os.path.exists(font_path) if font_path else False}")
        
        if font_path and os.path.exists(font_path):
            supports_japanese = translator.test_japanese_support(font_path)
            print(f"   日文支持: {'✅ 支持' if supports_japanese else '❌ 不支持'}")
            
            # 详细测试
            print(f"   详细测试:")
            try:
                from PIL import ImageFont
                font = ImageFont.truetype(font_path, 16)
                test_chars = ['あ', 'か', 'サ', 'ポ', '大', '切']
                
                for char in test_chars:
                    bbox = font.getbbox(char)
                    width = bbox[2] - bbox[0]
                    print(f"     '{char}': 宽度 {width}px {'✅' if width >= 8 else '❌'}")
            except Exception as e:
                print(f"     测试失败: {e}")
    else:
        print(f"   ❌ 字体未在匹配器中找到")
    
    print("\n4. 模拟demo.py中的检测逻辑:")
    matched_font = "Spoqa Han Sans Bold"
    
    # 获取匹配字体的真实文件路径
    font_path = None
    if matched_font in font_matcher.fonts:
        font_path = font_matcher.fonts[matched_font]
        
    # 使用真实路径检测日文支持
    supports_japanese = False
    if font_path and os.path.exists(font_path):
        supports_japanese = translator.test_japanese_support(font_path)
    
    print(f"   匹配字体: {matched_font}")
    print(f"   字体路径: {font_path}")
    print(f"   文件存在: {os.path.exists(font_path) if font_path else False}")
    print(f"   日文支持: {'✅ 支持' if supports_japanese else '❌ 不支持'}")
    
    if supports_japanese:
        print(f"   结果: 应该显示 '支持日文'")
    else:
        print(f"   结果: 应该显示 '不支持日文' 并使用思源黑体")

if __name__ == "__main__":
    test_font_support_detection() 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试fonts目录中各字体对日文的支持情况
"""

import os
import sys
import glob
from PIL import Image, ImageDraw, ImageFont

# 日文测试字符集
JAPANESE_TEST_CHARS = {
    'hiragana': 'あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわをん',
    'katakana': 'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン',
    'kanji': '日本語文字化学技術電子計算機情報処理',
    'common_words': ['こんにちは', 'ありがとう', '日本語', '文字化け', 'テスト', 'フォント']
}

def test_font_japanese_support(font_path, font_size=24):
    """测试字体对日文的支持程度"""
    if not os.path.exists(font_path):
        return None
    
    try:
        font = ImageFont.truetype(font_path, font_size)
        results = {}
        
        # 测试各类日文字符
        for char_type, test_chars in JAPANESE_TEST_CHARS.items():
            if char_type == 'common_words':
                # 测试常用词汇
                supported_words = []
                for word in test_chars:
                    try:
                        bbox = font.getbbox(word)
                        width = bbox[2] - bbox[0]
                        if width > 10:  # 如果渲染宽度合理，认为支持
                            supported_words.append(word)
                    except:
                        pass
                results[char_type] = {
                    'supported': len(supported_words),
                    'total': len(test_chars),
                    'examples': supported_words[:3]
                }
            else:
                # 测试单个字符
                supported = 0
                total = len(test_chars)
                examples = []
                
                for char in test_chars:
                    try:
                        bbox = font.getbbox(char)
                        width = bbox[2] - bbox[0]
                        if width > 8:  # 如果渲染宽度合理，认为支持
                            supported += 1
                            if len(examples) < 5:
                                examples.append(char)
                    except:
                        pass
                
                results[char_type] = {
                    'supported': supported,
                    'total': total,
                    'ratio': supported / total if total > 0 else 0,
                    'examples': examples
                }
        
        return results
    except Exception as e:
        return {'error': str(e)}

def analyze_fonts_in_directory():
    """分析fonts目录中所有字体的日文支持情况"""
    font_dir = '../fonts'
    if not os.path.exists(font_dir):
        print("错误：fonts目录不存在")
        return
    
    print("=== fonts目录日文字体支持分析 ===\n")
    
    # 查找所有字体文件
    font_files = []
    for root, dirs, files in os.walk(font_dir):
        for file in files:
            if file.lower().endswith(('.ttf', '.otf', '.ttc')):
                font_files.append(os.path.join(root, file))
    
    if not font_files:
        print("未找到字体文件")
        return
    
    print(f"找到 {len(font_files)} 个字体文件:\n")
    
    results = {}
    for font_path in font_files:
        rel_path = os.path.relpath(font_path, font_dir)
        font_name = os.path.basename(font_path)
        
        print(f"测试字体: {rel_path}")
        
        test_result = test_font_japanese_support(font_path)
        if test_result:
            results[rel_path] = test_result
            
            if 'error' in test_result:
                print(f"  ❌ 测试失败: {test_result['error']}")
            else:
                # 计算总体支持率
                total_support = 0
                total_chars = 0
                for char_type in ['hiragana', 'katakana', 'kanji']:
                    if char_type in test_result:
                        total_support += test_result[char_type]['supported']
                        total_chars += test_result[char_type]['total']
                
                overall_ratio = total_support / total_chars if total_chars > 0 else 0
                
                if overall_ratio > 0.9:
                    status = "✅ 完全支持"
                elif overall_ratio > 0.7:
                    status = "🟨 良好支持"
                elif overall_ratio > 0.3:
                    status = "🟧 部分支持"
                else:
                    status = "❌ 不支持"
                
                print(f"  {status} ({overall_ratio:.1%})")
                print(f"    平假名: {test_result['hiragana']['supported']}/{test_result['hiragana']['total']} ({test_result['hiragana']['ratio']:.1%})")
                print(f"    片假名: {test_result['katakana']['supported']}/{test_result['katakana']['total']} ({test_result['katakana']['ratio']:.1%})")
                print(f"    汉字: {test_result['kanji']['supported']}/{test_result['kanji']['total']} ({test_result['kanji']['ratio']:.1%})")
                
                if 'common_words' in test_result:
                    words_result = test_result['common_words']
                    print(f"    常用词: {words_result['supported']}/{words_result['total']} - {', '.join(words_result['examples'])}")
        
        print()
    
    # 生成推荐总结
    print("=" * 60)
    print("推荐总结:")
    print("=" * 60)
    
    excellent_fonts = []
    good_fonts = []
    partial_fonts = []
    
    for font_path, result in results.items():
        if 'error' not in result:
            total_support = 0
            total_chars = 0
            for char_type in ['hiragana', 'katakana', 'kanji']:
                if char_type in result:
                    total_support += result[char_type]['supported']
                    total_chars += result[char_type]['total']
            
            overall_ratio = total_support / total_chars if total_chars > 0 else 0
            
            if overall_ratio > 0.9:
                excellent_fonts.append((font_path, overall_ratio))
            elif overall_ratio > 0.7:
                good_fonts.append((font_path, overall_ratio))
            elif overall_ratio > 0.3:
                partial_fonts.append((font_path, overall_ratio))
    
    if excellent_fonts:
        print("🏆 最佳日文支持 (>90%):")
        for font, ratio in sorted(excellent_fonts, key=lambda x: x[1], reverse=True):
            print(f"  • {font} ({ratio:.1%})")
    
    if good_fonts:
        print("\n👍 良好日文支持 (70-90%):")
        for font, ratio in sorted(good_fonts, key=lambda x: x[1], reverse=True):
            print(f"  • {font} ({ratio:.1%})")
    
    if partial_fonts:
        print("\n⚠️ 部分日文支持 (30-70%):")
        for font, ratio in sorted(partial_fonts, key=lambda x: x[1], reverse=True):
            print(f"  • {font} ({ratio:.1%})")
    
    print(f"\n总计分析了 {len(results)} 个字体文件")

def create_japanese_test_image():
    """创建日文字体测试图片"""
    font_dir = '../fonts'
    output_path = '../test_output/japanese_font_test.png'
    os.makedirs('../test_output', exist_ok=True)
    
    # 查找支持日文的字体
    good_fonts = []
    for root, dirs, files in os.walk(font_dir):
        for file in files:
            if file.lower().endswith(('.ttf', '.otf', '.ttc')):
                font_path = os.path.join(root, file)
                result = test_font_japanese_support(font_path, 32)
                if result and 'error' not in result:
                    total_support = 0
                    total_chars = 0
                    for char_type in ['hiragana', 'katakana', 'kanji']:
                        if char_type in result:
                            total_support += result[char_type]['supported']
                            total_chars += result[char_type]['total']
                    
                    overall_ratio = total_support / total_chars if total_chars > 0 else 0
                    if overall_ratio > 0.7:  # 只显示支持度超过70%的字体
                        good_fonts.append((font_path, overall_ratio))
    
    if not good_fonts:
        print("未找到支持日文的字体")
        return
    
    # 创建测试图片
    test_text = "日本語フォントテスト\nこんにちは世界\nありがとうございます"
    img_width = 800
    img_height = len(good_fonts) * 120 + 100
    
    img = Image.new('RGB', (img_width, img_height), 'white')
    draw = ImageDraw.Draw(img)
    
    # 标题
    title_y = 20
    draw.text((20, title_y), "日文字体支持测试 - Japanese Font Support Test", fill='black')
    
    # 为每个字体绘制测试文字
    y_offset = 80
    for i, (font_path, ratio) in enumerate(sorted(good_fonts, key=lambda x: x[1], reverse=True)):
        try:
            font = ImageFont.truetype(font_path, 24)
            font_name = os.path.basename(font_path)
            
            # 字体名称和支持率
            draw.text((20, y_offset), f"{font_name} ({ratio:.1%})", fill='blue')
            
            # 测试文字
            draw.text((20, y_offset + 30), test_text, font=font, fill='black')
            
            y_offset += 120
        except Exception as e:
            print(f"绘制字体 {font_path} 时出错: {e}")
    
    img.save(output_path)
    print(f"日文字体测试图片已保存: {output_path}")

if __name__ == "__main__":
    analyze_fonts_in_directory()
    print("\n正在生成日文字体测试图片...")
    create_japanese_test_image() 
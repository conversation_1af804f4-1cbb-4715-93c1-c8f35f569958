""" 
方法一：像素级测高
------------------
本脚本演示如何在原图中对中文文字做 *像素级高度测量*，
并将该实测高度作为日文文字高度匹配的目标。

运行后将在 output/method1_result.png 生成替换后的图片，
并在控制台打印翻译与高度日志。

依赖：
    pip install easyocr pillow opencv-python numpy
"""

import sys, os  # 新增
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import cv2
import numpy as np
from core.translator import SimpleTranslator
from PIL import Image, ImageDraw, ImageFont
from paddleocr import PaddleOCR

# -------------------------------
#  辅助：检测中文字符
# -------------------------------

def contains_chinese(text: str) -> bool:
    """判断字符串里是否包含至少一个 CJK 统一表意字符"""
    for ch in text:
        if '\u4e00' <= ch <= '\u9fff':
            return True
    return False


# -------------------------------
#  方法一专用的 Translator
# -------------------------------

class PixelHeightTranslator(SimpleTranslator):
    """在高度匹配时使用 *原文字体像素高度* 作为目标高度"""

    @staticmethod
    def _measure_real_text_height(region_img: np.ndarray) -> int:
        """像素级测量前景文字的高度"""
        if region_img is None or region_img.size == 0:
            return 0

        # 灰度 + OTSU
        gray = cv2.cvtColor(region_img, cv2.COLOR_BGR2GRAY) if len(region_img.shape) == 3 else region_img
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # 确保文字为前景(0)，背景255
        if np.sum(binary == 0) < np.sum(binary == 255):
            binary = cv2.bitwise_not(binary)

        # 寻找所有连通域
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            return region_img.shape[0]

        xs = []
        ys = []
        for c in contours:
            x, y, w, h = cv2.boundingRect(c)
            xs.extend([x, x + w])
            ys.extend([y, y + h])
        real_h = max(ys) - min(ys)
        return real_h

    # --- 重写核心布局计算 ---
    def calculate_layout_aware_layout(self, region, original_image, layout_result):  # noqa: N802 – 保持原方法名
        x, y, w, h = region['bbox']
        font_path = self.font_mapping.get(region['font_info']['font'])

        # 像素级测高
        text_region = self._current_original_image[y : y + h, x : x + w]
        real_cn_h = self._measure_real_text_height(text_region)
        if real_cn_h <= 0:
            real_cn_h = h  # 退化到外接框高度

        # 与原实现相同：通过高度匹配算法求字号
        base_font_size = max(region['style_info']['estimated_font_size'], 16)
        font_size = self.calculate_font_size_by_height_matching(
            region['translated_text'], real_cn_h, font_path, base_font_size
        )

        # 计算文字尺寸 & 位置居中
        text_width, text_height = self.get_text_dimensions(
            region['translated_text'], font_path, font_size
        )
        text_x = x + (w - text_width) // 2
        text_y = y + (h - text_height) // 2
        text_x = max(0, text_x)
        text_y = max(0, text_y)

        region.update(
            {
                'font_size': font_size,
                'text_width': text_width,
                'text_height': text_height,
                'text_x': text_x,
                'text_y': text_y,
            }
        )

    # --- 轻量包装主流程，存储 original_image 供测高 ---
    def process_translation_results(self, image, chinese_regions, font_info, original_image, layout_result=None):  # noqa: D401
        self._current_original_image = original_image  # 临时保存
        return super().process_translation_results(image, chinese_regions, font_info, original_image, layout_result)


# -------------------------------
#  主流程
# -------------------------------

def main():
    img_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'example.jpg')
    if not os.path.exists(img_path):
        raise FileNotFoundError(f'找不到 {img_path}')

    image = cv2.imread(img_path)
    if image is None:
        raise RuntimeError('图片读取失败')

    # ----- 使用 easyocr 做最简单的中文检测 -----
    try:
        import easyocr
    except ImportError:
        raise SystemExit('请先安装 easyocr :  pip install easyocr')

    reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)  # 移除不支持的 japanese
    ocr_results = reader.readtext(image)

    chinese_regions = []  # [(poly, text, conf), ...]
    font_info = []  # [{'text': str, 'font': str, 'confidence': float}, ...]

    for (bbox, text, conf) in ocr_results:
        if not contains_chinese(text):
            continue
        poly = [(int(pt[0]), int(pt[1])) for pt in bbox]
        chinese_regions.append((poly, text, conf))
        font_info.append({'text': text, 'font': 'Spoqa Han Sans Bold', 'confidence': conf})

    if not chinese_regions:
        print('未检测到中文区域，脚本结束。')
        return

    translator = PixelHeightTranslator()
    result_img, logs = translator.process_translation_results(image, chinese_regions, font_info, image)

    out_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'output', 'method1_result.png')
    os.makedirs(os.path.dirname(out_path), exist_ok=True)
    cv2.imwrite(out_path, result_img)

    print('\n====== 翻译与高度日志 ======')
    for item in logs:
        print(f"{item['original']} -> {item['translated']} (字体: {item['font']}, 调整: {item['adjustments']})")
    print(f'结果已保存至 {out_path}')


def compare_height_methods():
    """比较不同高度测量方法的结果"""
    translator = SimpleTranslator()
    
    IMG_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'example.jpg')
    
    # 初始化OCR
    ocr = PaddleOCR(use_angle_cls=False, lang='ch')
    ocr_res_list = ocr.predict(input=IMG_PATH)
    
    # 构建OCR检测结果映射
    detected_map = {}
    for res in ocr_res_list:
        dt_polys = res.get('dt_polys', [])
        rec_texts = res.get('rec_texts', [])
        for poly, txt in zip(dt_polys, rec_texts):
            pts = np.array(poly, dtype=np.int32)
            x, y, w, h = cv2.boundingRect(pts)
            detected_map[txt] = (x, y, w, h)
    
    # 测试目标文本
    target_texts = ["久睡不塌", "适用更久"]
    
    print("=== 高度测量方法比较 ===")
    print("方法1: OCR外接矩形高度")
    print("方法2: 像素级ROI笔画高度测量")
    print("方法3: 字体渲染高度测量")
    print()
    
    for text in target_texts:
        if text not in detected_map:
            print(f"警告: 未在OCR结果中找到 '{text}'")
            continue
            
        x, y, w, h = detected_map[text]
        print(f"文本: '{text}'")
        print(f"  方法1 - OCR外接矩形高度: {h}px")
        
        # 提取ROI
        orig_img = cv2.imread(IMG_PATH)
        roi = orig_img[y:y+h, x:x+w].copy()
        
        # 方法2: 像素级ROI笔画高度测量
        pixel_height = translator.measure_roi_text_height(roi, prefix=f'method2_{text}')
        print(f"  方法2 - 像素级ROI笔画高度: {pixel_height}px")
        
        # 方法3: 字体渲染高度测量（使用不同字号）
        font_path = os.path.join("fonts", "台北黑体", "TaipeiSans-Bold.ttf")
        test_sizes = [h-5, h, h+5, pixel_height-5, pixel_height, pixel_height+5]
        
        print(f"  方法3 - 字体渲染高度测量:")
        for size in test_sizes:
            if size > 0:
                rendered_height = translator.measure_rendered_text_height(text, font_path, size)
                print(f"    字号{size}px → 渲染高度{rendered_height}px")
        
        print()


if __name__ == '__main__':
    main()
    compare_height_methods() 
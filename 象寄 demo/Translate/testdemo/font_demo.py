import cv2
import numpy as np
from paddleocr import PaddleOCR
import os
import re
from ..core.font_matcher import SimpleFontMatcher
from PIL import Image, ImageDraw, ImageFont

def is_chinese_text(text):
    """判断文本是否包含中文字符"""
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
    return bool(chinese_pattern.search(text))

def enhanced_font_recognition_test(save_debug_images=False):
    """增强的字体识别测试函数
    
    Args:
        save_debug_images (bool): 是否保存调试用的中间图片，默认False
    """
    
    # 检查输入文件
    img_path = '../example.jpg'
    if not os.path.exists(img_path):
        print(f"错误：找不到图像文件 {img_path}")
        return
    
    print(f"=== 字体识别测试程序 ===")
    print(f"处理图像: {img_path}")
    
    # 初始化OCR
    print("初始化PaddleOCR...")
    try:
        ocr = PaddleOCR(
            use_doc_orientation_classify=False,
            use_doc_unwarping=False,
            use_textline_orientation=False
        )
        print("✓ PaddleOCR初始化完成")
    except Exception as e:
        print(f"✗ PaddleOCR初始化失败: {e}")
        return
    
    # 初始化字体匹配器
    print("初始化字体匹配器...")
    try:
        font_matcher = SimpleFontMatcher()
        print("✓ 字体匹配器初始化完成")
        print(f"可用字体数量: {len(font_matcher.fonts)}")
    except Exception as e:
        print(f"✗ 字体匹配器初始化失败: {e}")
        return
    
    # OCR文字识别
    print("\n执行OCR文字识别...")
    result = ocr.predict(input=img_path)
    if not result:
        print("✗ 未检测到任何文字")
        return
    
    # 读取原始图像
    cv_image = cv2.imread(img_path)
    if cv_image is None:
        print("✗ 无法读取图像文件")
        return
    
    # 如果需要保存调试图片，创建输出目录
    if save_debug_images:
        os.makedirs("../test_output", exist_ok=True)
    
    print("✓ OCR识别完成，开始分析文字区域...")
    
    # 处理OCR结果
    total_regions = 0
    chinese_regions = 0
    font_analysis_results = []
    first_font_comparison_saved = False  # 仅保存第一个中文区域的比较图
    
    for res in result:
        if 'rec_texts' in res and 'dt_polys' in res:
            rec_texts = res['rec_texts']
            dt_polys = res['dt_polys'] 
            rec_scores = res.get('rec_scores', [1.0] * len(rec_texts))
            
            total_regions = len(rec_texts)
            print(f"\n检测到 {total_regions} 个文字区域")
            print("=" * 60)
            
            for i, (text, poly, score) in enumerate(zip(rec_texts, dt_polys, rec_scores), 1):
                is_chinese = is_chinese_text(text)
                lang_type = "中文" if is_chinese else "其他"
                print(f"\n区域 {i}: '{text}'")
                print(f"  语言类型: {lang_type}")
                print(f"  置信度: {score:.3f}")
                
                if is_chinese:
                    chinese_regions += 1
                    
                    # 提取文字区域
                    points = np.array(poly, dtype=np.int32)
                    x, y, w, h = cv2.boundingRect(points)
                    
                    # 扩展边界框以获得更好的字体识别效果
                    padding = 5
                    x = max(0, x - padding)
                    y = max(0, y - padding)
                    w = min(cv_image.shape[1] - x, w + 2*padding)
                    h = min(cv_image.shape[0] - y, h + 2*padding)
                    
                    text_region = cv_image[y:y+h, x:x+w]
                    
                    if text_region.size > 0:
                        print(f"  文字区域尺寸: {w}×{h}")
                        
                        # 可选：保存单独的文字区域图像以便调试
                        if save_debug_images:
                            region_path = f"../test_output/text_region_{i}.png"
                            cv2.imwrite(region_path, text_region)
                            print(f"  区域图像已保存: {region_path}")
                        
                        # 字体匹配
                        print(f"  正在进行字体匹配...")
                        try:
                            matched_font, font_confidence = font_matcher.match_font(text_region, text)
                            
                            result_info = {
                                'region_id': i,
                                'text': text,
                                'ocr_confidence': score,
                                'matched_font': matched_font,
                                'font_confidence': font_confidence,
                                'region_size': (w, h),
                                'is_reliable': font_confidence > 0.3 and score > 0.7
                            }
                            font_analysis_results.append(result_info)
                            
                            print(f"  → 最佳匹配字体: {matched_font}")
                            print(f"  → 字体匹配置信度: {font_confidence:.3f}")
                            
                            reliability = "可靠" if result_info['is_reliable'] else "不可靠"
                            print(f"  → 结果可靠性: {reliability}")
                            
                            # 保存第一段文字的字体比较图（含所有字体标签）
                            if not first_font_comparison_saved:
                                comparison_dir = "../test_output"
                                font_matcher.save_comparison(text_region, text, comparison_dir)
                                first_font_comparison_saved = True
                            elif save_debug_images:
                                # 如果需要完整调试，仍可保存每个区域的比较图
                                comparison_dir = f"../test_output/font_comparison_region_{i}"
                                font_matcher.save_comparison(text_region, text, comparison_dir)
                            
                        except Exception as e:
                            print(f"  ✗ 字体匹配失败: {e}")
                    else:
                        print(f"  ✗ 无效的文字区域")
                else:
                    print(f"  → 跳过非中文文字")
    
    # 生成结果统计
    print("\n" + "=" * 60)
    print("字体识别结果统计")
    print("=" * 60)
    print(f"总文字区域数: {total_regions}")
    print(f"中文文字区域数: {chinese_regions}")
    print(f"字体分析完成数: {len(font_analysis_results)}")
    
    if font_analysis_results:
        # 按置信度排序
        font_analysis_results.sort(key=lambda x: x['font_confidence'], reverse=True)
        
        print(f"\n详细分析结果 (按字体匹配置信度排序):")
        print("-" * 80)
        for i, result in enumerate(font_analysis_results, 1):
            reliability_icon = "✓" if result['is_reliable'] else "?"
            print(f"{reliability_icon} {i}. 区域{result['region_id']}: '{result['text']}'")
            print(f"   字体: {result['matched_font']} (置信度: {result['font_confidence']:.3f})")
            print(f"   OCR置信度: {result['ocr_confidence']:.3f}")
            print(f"   区域尺寸: {result['region_size'][0]}×{result['region_size'][1]}")
            print()
        
        # 字体使用统计
        font_stats = {}
        reliable_results = [r for r in font_analysis_results if r['is_reliable']]
        
        for result in reliable_results:
            font = result['matched_font']
            if font in font_stats:
                font_stats[font] += 1
            else:
                font_stats[font] = 1
        
        if font_stats:
            print("可靠的字体识别统计:")
            print("-" * 40)
            for font, count in sorted(font_stats.items(), key=lambda x: x[1], reverse=True):
                print(f"  {font}: {count} 个区域")
        else:
            print("未找到可靠的字体识别结果")
        
        # 识别质量分析
        high_confidence_count = len([r for r in font_analysis_results if r['font_confidence'] > 0.5])
        medium_confidence_count = len([r for r in font_analysis_results if 0.3 <= r['font_confidence'] <= 0.5])
        low_confidence_count = len([r for r in font_analysis_results if r['font_confidence'] < 0.3])
        
        print(f"\n字体识别质量分析:")
        print("-" * 30)
        print(f"高置信度 (>0.5): {high_confidence_count} 个")
        print(f"中等置信度 (0.3-0.5): {medium_confidence_count} 个")
        print(f"低置信度 (<0.3): {low_confidence_count} 个")
        
        if chinese_regions > 0:
            success_rate = (high_confidence_count + medium_confidence_count) / chinese_regions * 100
            print(f"总体成功率: {success_rate:.1f}%")
    else:
        print("未完成任何字体分析")
    
    # 可视化结果
    print(f"\n生成可视化结果...")
    
    # 在原图上标记文字区域和字体识别结果
    result_image = cv_image.copy()
    
    # -------- 使用 Pillow 绘制中文标签 --------
    # 先用 OpenCV 画框，再用 Pillow 画文字，确保中文可显示
    # 1. 使用 OpenCV 画多边形框
    for res in result:
        if 'rec_texts' in res and 'dt_polys' in res:
            rec_texts = res['rec_texts']
            dt_polys = res['dt_polys']
            
            for i, (text, poly) in enumerate(zip(rec_texts, dt_polys), 1):
                if is_chinese_text(text):
                    points = np.array(poly, dtype=np.int32)
                    matched_result = next((r for r in font_analysis_results if r['region_id'] == i), None)
                    if matched_result:
                        color = (0, 255, 0) if matched_result['is_reliable'] else (0, 165, 255)
                        cv2.polylines(result_image, [points], True, color, 2)

    # 2. 转为 Pillow 图像
    pil_img = Image.fromarray(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(pil_img)

    # 准备字体
    try:
        label_font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 18)
    except Exception:
        try:
            label_font = ImageFont.truetype("C:/Windows/Fonts/simsun.ttc", 18)
        except Exception:
            label_font = ImageFont.load_default()

    for res in result:
        if 'rec_texts' in res and 'dt_polys' in res:
            rec_texts = res['rec_texts']
            dt_polys = res['dt_polys']
            for i, (text, poly) in enumerate(zip(rec_texts, dt_polys), 1):
                if is_chinese_text(text):
                    matched_result = next((r for r in font_analysis_results if r['region_id'] == i), None)
                    if matched_result:
                        color_rgb = (0, 255, 0) if matched_result['is_reliable'] else (255, 165, 0)  # 注意Pillow是RGB
                        label = f"{i}: {matched_result['matched_font']}"

                        # 坐标
                        points = np.array(poly, dtype=np.int32)
                        x, y = points[0]

                        # 文字尺寸
                        text_bbox = draw.textbbox((0,0), label, font=label_font)
                        label_w = text_bbox[2] - text_bbox[0]
                        label_h = text_bbox[3] - text_bbox[1]

                        # 背景矩形
                        draw.rectangle([(x, y - label_h - 8), (x + label_w + 6, y)], fill=color_rgb)
                        # 文字
                        draw.text((x + 3, y - label_h - 4), label, fill=(255, 255, 255), font=label_font)

    # 转回 OpenCV 图像
    result_image = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
    
    # 可选：保存可视化结果
    if save_debug_images:
        output_path = "output/font_recognition_result.png"
        cv2.imwrite(output_path, result_image)
        print(f"✓ 可视化结果已保存: {output_path}")
        print(f"\n=== 字体识别测试完成 ===")
        print(f"详细结果请查看 output/ 目录")
    else:
        print(f"\n=== 字体识别测试完成 ===")
        print(f"注意：未保存调试图片（节省存储空间）")

if __name__ == "__main__":
    try:
        # 可以通过修改这里的参数来控制是否保存调试图片
        # save_debug_images=True: 保存所有调试图片（当前目录会有很多文件）
        # save_debug_images=False: 不保存调试图片，只在内存中处理（推荐）
        enhanced_font_recognition_test(save_debug_images=False)
    except Exception as e:
        print(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n按 Enter 键退出...")
    input()

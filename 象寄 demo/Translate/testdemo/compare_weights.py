#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比不同粗细值的翻译效果
"""

import os
import shutil
from PIL import Image

def run_demo_with_weight(weight):
    """运行demo.py并指定粗细值"""
    import sys
    import importlib.util
    
    # 动态修改demo.py中的FONT_WEIGHT值
    demo_path = "../demo.py"
    
    # 读取demo.py内容
    with open(demo_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换FONT_WEIGHT值
    import re
    pattern = r'FONT_WEIGHT = \d+'
    new_content = re.sub(pattern, f'FONT_WEIGHT = {weight}', content)
    
    # 临时保存修改后的内容
    temp_demo_path = f"demo_temp_{weight}.py"
    with open(temp_demo_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    try:
        # 动态导入并执行
        spec = importlib.util.spec_from_file_location("demo_temp", temp_demo_path)
        demo_module = importlib.util.module_from_spec(spec)
        
        # 重定向输出文件名
        original_output = "../output/final_translated.png"
        weight_output = f"../test_output/final_translated_weight_{weight}.png"
        
        # 执行demo
        print(f"\n=== 生成粗细值 {weight} 的翻译结果 ===")
        spec.loader.exec_module(demo_module)
        
        # 复制结果到指定文件名
        if os.path.exists(original_output):
            shutil.copy2(original_output, weight_output)
            print(f"结果已保存为: {weight_output}")
        
    except Exception as e:
        print(f"执行粗细值 {weight} 时出错: {e}")
    finally:
        # 清理临时文件
        if os.path.exists(temp_demo_path):
            os.remove(temp_demo_path)

def create_comparison_image():
    """创建粗细对比图"""
    weights = [300, 400, 600, 900]
    images = []
    
    for weight in weights:
        img_path = f"../test_output/final_translated_weight_{weight}.png"
        if os.path.exists(img_path):
            img = Image.open(img_path)
            images.append((weight, img))
    
    if not images:
        print("没有找到可对比的图片")
        return
    
    # 创建对比图
    img_width = images[0][1].width
    img_height = images[0][1].height
    comparison_width = img_width * 2
    comparison_height = img_height * 2
    
    comparison = Image.new('RGB', (comparison_width, comparison_height), 'white')
    
    positions = [(0, 0), (img_width, 0), (0, img_height), (img_width, img_height)]
    
    for i, (weight, img) in enumerate(images[:4]):
        x, y = positions[i]
        comparison.paste(img, (x, y))
        
        # 添加标签 (简单的文字叠加)
        from PIL import ImageDraw, ImageFont
        draw = ImageDraw.Draw(comparison)
        try:
            font = ImageFont.truetype("Arial", 24)
        except:
            font = ImageFont.load_default()
        
        label = f"Weight: {weight}"
        draw.text((x + 10, y + 10), label, fill='red', font=font)
    
    comparison_path = "../test_output/weight_comparison.png"
    comparison.save(comparison_path)
    print(f"对比图已保存: {comparison_path}")

if __name__ == "__main__":
    os.makedirs("../test_output", exist_ok=True)
    
    # 测试不同粗细值
    test_weights = [300, 400, 600, 900]
    
    for weight in test_weights:
        run_demo_with_weight(weight)
    
    # 创建对比图
    create_comparison_image()
    
    print("\n所有对比测试完成！") 
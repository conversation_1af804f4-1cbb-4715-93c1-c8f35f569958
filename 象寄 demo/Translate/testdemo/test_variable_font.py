#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试可变字体粗细调节功能
"""

import os
from PIL import Image, ImageDraw, ImageFont

def test_variable_font_weights():
    """测试不同粗细的可变字体效果"""
    vf_path = os.path.join('..', 'fonts', '思源黑体', 'SourceHanSans-VF.otf')
    
    if not os.path.exists(vf_path):
        print(f"可变字体文件不存在: {vf_path}")
        return
    
    # 测试不同的粗细值
    weights = [100, 200, 300, 400, 500, 600, 700, 800, 900]
    weight_names = ['超细', '细', '较细', '正常', '中等', '半粗', '粗', '超粗', '黑体']
    
    test_text = "テスト文字"
    font_size = 48
    
    # 创建测试图像
    img_width = 600
    img_height = len(weights) * 80
    img = Image.new('RGB', (img_width, img_height), 'white')
    draw = ImageDraw.Draw(img)
    
    # 为每个粗细值生成文字
    for i, (weight, name) in enumerate(zip(weights, weight_names)):
        y = i * 80 + 20
        
        try:
            # 加载字体
            font = ImageFont.truetype(vf_path, font_size)
            
            # 检查是否为可变字体
            axes = font.get_variation_axes()
            print(f"可变字体轴信息: {axes}")
            
            # 设置粗细
            if axes:
                weight_axis = None
                for axis in axes:
                    axis_name = axis.get('name', b'')
                    if isinstance(axis_name, bytes):
                        axis_name_str = axis_name.decode('utf-8', errors='ignore').lower()
                    else:
                        axis_name_str = str(axis_name).lower()
                    
                    if (axis_name == b'wght' or 
                        axis_name == b'Weight' or 
                        'weight' in axis_name_str or
                        'wght' in axis_name_str):
                        weight_axis = axis
                        break
                
                if weight_axis:
                    min_weight = weight_axis.get('minimum', 100)
                    max_weight = weight_axis.get('maximum', 900)
                    adjusted_weight = max(min_weight, min(max_weight, weight))
                    
                    # 使用正确的方法设置可变字体粗细
                    axis_name = weight_axis.get('name', b'')
                    try:
                        # 使用轴索引列表方式设置，需要找到weight轴的索引
                        weight_axis_index = None
                        for idx, axis in enumerate(axes):
                            axis_name = axis.get('name', b'')
                            if isinstance(axis_name, bytes):
                                axis_name_str = axis_name.decode('utf-8', errors='ignore').lower()
                            else:
                                axis_name_str = str(axis_name).lower()
                            
                            if (axis_name == b'wght' or 
                                axis_name == b'Weight' or 
                                'weight' in axis_name_str or
                                'wght' in axis_name_str):
                                weight_axis_index = idx
                                break
                        
                        if weight_axis_index is not None:
                            # 创建轴值列表，只修改weight轴
                            axis_values = [axis.get('default', 400) for axis in axes]
                            axis_values[weight_axis_index] = adjusted_weight
                            font.set_variation_by_axes(axis_values)
                        else:
                            print(f"      找不到weight轴索引")
                            continue
                    except Exception as e:
                        print(f"      设置粗细失败: {e}")
                        continue
                    
                    # 绘制文字
                    draw.text((20, y), f"Weight {weight} ({name}): {test_text}", 
                             font=font, fill='black')
                    
                    print(f"Weight {weight} ({name}) - 调整后: {adjusted_weight}")
                else:
                    print(f"Weight {weight} - 未找到weight轴")
            else:
                print(f"Weight {weight} - 不是可变字体")
                
        except Exception as e:
            print(f"Weight {weight} - 错误: {e}")
            # 使用默认字体绘制错误信息
            draw.text((20, y), f"Weight {weight} ({name}): Error", 
                     fill='red')
    
    # 保存测试结果
    output_path = "../test_output/variable_font_test.png"
    os.makedirs("../test_output", exist_ok=True)
    img.save(output_path)
    print(f"测试结果已保存: {output_path}")

if __name__ == "__main__":
    test_variable_font_weights() 
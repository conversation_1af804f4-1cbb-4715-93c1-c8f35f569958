""" 
方法二：字面高度 / 区域高度 比例纠正
--------------------------------------
本脚本演示在保持外接框高度的前提下，
使用 ratio = (原文字像素高度 / 外接框高度)
来修正日文字体高度的策略。
结果保存至 output/method2_result.png。

依赖：
    pip install easyocr pillow opencv-python numpy
"""

import sys, os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import cv2
import numpy as np
from core.translator import SimpleTranslator

# -------------------------------
#  辅助：检测中文字符
# -------------------------------

def contains_chinese(text: str) -> bool:
    for ch in text:
        if '\u4e00' <= ch <= '\u9fff':
            return True
    return False


# -------------------------------
#  方法二专用的 Translator
# -------------------------------

class RatioHeightTranslator(SimpleTranslator):
    """高度目标 = 外接框高度 × ratio(真实文字/框高)"""

    @staticmethod
    def _measure_real_text_height(region_img: np.ndarray) -> int:
        if region_img is None or region_img.size == 0:
            return 0
        gray = cv2.cvtColor(region_img, cv2.COLOR_BGR2GRAY) if len(region_img.shape) == 3 else region_img
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        if np.sum(binary == 0) < np.sum(binary == 255):
            binary = cv2.bitwise_not(binary)
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            return region_img.shape[0]
        ys = []
        for c in contours:
            _, y, _, h = cv2.boundingRect(c)
            ys.extend([y, y + h])
        return max(ys) - min(ys)

    def calculate_height_matched_layout(self, region):  # noqa: N802
        x, y, w, h = region['bbox']
        font_path = self.font_mapping.get(region['font_info']['font'])

        text_region = self._current_original_image[y : y + h, x : x + w]
        real_cn_h = self._measure_real_text_height(text_region)
        if real_cn_h <= 0:
            real_cn_h = h

        ratio = real_cn_h / max(h, 1)
        target_height = h * ratio

        base_font_size = max(region['style_info']['estimated_font_size'], 16)
        font_size = self.calculate_font_size_by_height_matching(
            region['translated_text'], target_height, font_path, base_font_size
        )

        text_width, text_height = self.get_text_dimensions(
            region['translated_text'], font_path, font_size
        )
        text_x = x + (w - text_width) // 2
        text_y = y + (h - text_height) // 2
        text_x = max(0, text_x)
        text_y = max(0, text_y)

        region.update(
            {
                'font_size': font_size,
                'text_width': text_width,
                'text_height': text_height,
                'text_x': text_x,
                'text_y': text_y,
            }
        )

    def process_translation_results(self, image, chinese_regions, font_info, original_image):  # noqa: D401
        self._current_original_image = original_image
        return super().process_translation_results(image, chinese_regions, font_info, original_image)


# -------------------------------
#  主流程
# -------------------------------

def main():
    img_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'example.jpg')
    if not os.path.exists(img_path):
        raise FileNotFoundError(f'找不到 {img_path}')

    image = cv2.imread(img_path)
    if image is None:
        raise RuntimeError('图片读取失败')

    try:
        import easyocr
    except ImportError:
        raise SystemExit('请先安装 easyocr :  pip install easyocr')

    reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)  # 移除不支持的 japanese
    ocr_results = reader.readtext(image)

    chinese_regions = []
    font_info = []
    for bbox, text, conf in ocr_results:
        if not contains_chinese(text):
            continue
        poly = [(int(pt[0]), int(pt[1])) for pt in bbox]
        chinese_regions.append((poly, text, conf))
        font_info.append({'text': text, 'font': 'Spoqa Han Sans Bold', 'confidence': conf})

    if not chinese_regions:
        print('未检测到中文区域，脚本结束。')
        return

    translator = RatioHeightTranslator()
    result_img, logs = translator.process_translation_results(image, chinese_regions, font_info, image)

    out_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'output', 'method2_result.png')
    os.makedirs(os.path.dirname(out_path), exist_ok=True)
    cv2.imwrite(out_path, result_img)

    print('\n====== 翻译与高度日志 ======')
    for item in logs:
        print(f"{item['original']} -> {item['translated']} (字体: {item['font']}, 调整: {item['adjustments']})")
    print(f'结果已保存至 {out_path}')


if __name__ == '__main__':
    main() 
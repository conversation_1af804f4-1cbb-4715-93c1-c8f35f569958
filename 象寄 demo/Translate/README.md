# 图像翻译项目

这是一个基于OCR和字体匹配的中日文图像翻译项目。

## 项目结构

```
Translate/
├── demo.py                 # 主演示程序
├── example.jpg            # 示例图片
├── *.jpg                  # 其他测试图片
├── README.md              # 项目说明
├── core/                  # 核心功能模块
│   ├── __init__.py
│   ├── font_matcher.py    # 字体匹配核心
│   └── translator.py      # 翻译核心
├── utils/                 # 工具模块
│   ├── __init__.py
│   ├── enhanced_font_manager.py
│   ├── font_downloader.py
│   ├── font_scanner.py
│   ├── download_fonts.py
│   ├── system_fonts.json
│   └── font_glyphs_cache.json
├── testdemo/             # 测试演示
│   ├── __init__.py
│   ├── font_demo.py      # 字体匹配测试
│   ├── test_variable_font.py  # 可变字体测试
│   └── compare_weights.py     # 字体粗细对比
├── fonts/                # 字体文件目录
│   ├── 思源黑体/
│   ├── NotoSansSC/
│   └── ... (其他字体)
├── output/               # 主程序输出目录
└── test_output/          # 测试程序输出目录
```

## 主要功能

1. **OCR文字识别** - 使用PaddleOCR识别图像中的中文文字
2. **字体匹配** - 分析原文字体风格并匹配合适的日文字体
3. **中日翻译** - 基于词典的中文到日文翻译
4. **可变字体支持** - 支持思源黑体等可变字体的粗细调节
5. **高度匹配** - 确保译文与原文在视觉高度上一致

## 使用方法

### 运行主程序
```bash
python demo.py
```

### 测试字体匹配
```bash
cd testdemo
python font_demo.py
```

### 测试可变字体
```bash
cd testdemo
python test_variable_font.py
```

### 对比不同字体粗细
```bash
cd testdemo
python compare_weights.py
```

## 可调节参数

在 `demo.py` 中可以调节以下参数：

```python
# 思源黑体粗细设置 (100-900)
FONT_WEIGHT = 400  # 修改这个值来调整所有译文的粗细
```

- `100` = 超细 (Extra Light)
- `400` = 正常 (Regular) - 默认值
- `700` = 粗 (Bold)
- `900` = 黑体 (Heavy/Black)

## 输出说明

- **主程序输出**: `output/` 目录
  - `final_translated.png` - 最终翻译结果
  - `detection_simple.png` - OCR检测结果可视化
  - `chinese_text_removed.png` - 中文文字移除后的图像

- **测试程序输出**: `test_output/` 目录
  - 各种测试和对比图片

## 依赖环境

- Python 3.8+
- OpenCV
- PaddleOCR
- Pillow (需要支持可变字体的版本)
- NumPy
- scikit-learn

## 注意事项

1. 确保已安装支持可变字体的Pillow版本
2. 思源黑体可变字体文件需放在 `fonts/思源黑体/SourceHanSans-VF.otf`
3. 如需最佳效果，建议使用高分辨率的输入图片 